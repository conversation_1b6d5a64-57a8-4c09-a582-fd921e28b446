#!/usr/bin/env python3
"""
Test script to verify CORS configuration and API endpoints.
This script tests the Django backend API endpoints to ensure they're working correctly.
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:8000"
ENDPOINTS = [
    "/api/catalogue/categories/",
    "/api/catalogue/services/",
    "/api/catalogue/categories/tree/",
]

def test_endpoint(url, headers=None):
    """Test a single endpoint"""
    if headers is None:
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json',
        }
    
    try:
        print(f"\n🔍 Testing: {url}")
        
        # Test OPTIONS request (preflight)
        options_response = requests.options(url, headers=headers)
        print(f"   OPTIONS Status: {options_response.status_code}")
        
        if options_response.status_code == 200:
            cors_headers = {
                'Access-Control-Allow-Origin': options_response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Methods': options_response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': options_response.headers.get('Access-Control-Allow-Headers'),
                'Access-Control-Allow-Credentials': options_response.headers.get('Access-Control-Allow-Credentials'),
            }
            print(f"   CORS Headers: {json.dumps(cors_headers, indent=6)}")
        
        # Test GET request
        get_response = requests.get(url, headers=headers)
        print(f"   GET Status: {get_response.status_code}")
        
        if get_response.status_code == 200:
            data = get_response.json()
            if 'results' in data:
                print(f"   Results Count: {len(data['results'])}")
                print(f"   Total Count: {data.get('count', 'N/A')}")
            else:
                print(f"   Response Type: {type(data)}")
                if isinstance(data, list):
                    print(f"   Items Count: {len(data)}")
        else:
            print(f"   Error: {get_response.text}")
            
        return get_response.status_code == 200
        
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Connection Error: Could not connect to {url}")
        print(f"   Make sure Django server is running on {BASE_URL}")
        return False
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Django CORS Configuration and API Endpoints")
    print(f"Base URL: {BASE_URL}")
    print("=" * 60)
    
    success_count = 0
    total_count = len(ENDPOINTS)
    
    for endpoint in ENDPOINTS:
        full_url = BASE_URL + endpoint
        if test_endpoint(full_url):
            success_count += 1
            print("   ✅ Success")
        else:
            print("   ❌ Failed")
    
    print("\n" + "=" * 60)
    print(f"📊 Results: {success_count}/{total_count} endpoints working")
    
    if success_count == total_count:
        print("🎉 All tests passed! CORS is configured correctly.")
        print("\n💡 Your Next.js frontend should now be able to access the API.")
        print("   Try refreshing your frontend application.")
    else:
        print("⚠️  Some tests failed. Check the following:")
        print("   1. Is Django server running? (python manage.py runserver)")
        print("   2. Are there any database migration issues?")
        print("   3. Check Django logs for errors")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
