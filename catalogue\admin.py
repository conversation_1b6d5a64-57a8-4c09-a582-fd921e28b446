from django.contrib import admin
from mptt.admin import MPTTModelAdmin
from .models import Category, Service, Discount


@admin.register(Category)
class CategoryAdmin(MPTTModelAdmin):
    """
    Admin interface for Category model with MPTT tree structure.
    """
    list_display = ['name', 'parent', 'level', 'is_active', 'created_at']
    list_filter = ['is_active', 'level', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('name', 'slug', 'parent', 'description', 'image', 'is_active')
        }),
        ('SEO Settings', {
            'fields': ('meta_title', 'meta_description', 'og_image_url'),
            'classes': ('collapse',),
            'description': 'Search Engine Optimization and Social Media settings'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """
    Admin interface for Service model.
    """
    list_display = [
        'title', 'category', 'base_price', 'discount_price',
        'get_current_price', 'requires_partial_payment', 'is_active', 'created_at'
    ]
    list_filter = ['is_active', 'category', 'requires_partial_payment', 'created_at']
    search_fields = ['title', 'description', 'category__name']
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = [
        'created_at', 'updated_at', 'get_current_price', 'get_discount_percentage',
        'get_partial_payment_amount', 'get_remaining_payment_amount'
    ]

    fieldsets = (
        (None, {
            'fields': ('title', 'slug', 'category', 'description', 'image')
        }),
        ('Pricing', {
            'fields': ('base_price', 'discount_price', 'get_current_price', 'get_discount_percentage')
        }),
        ('Partial Payment Settings', {
            'fields': (
                'requires_partial_payment', 'partial_payment_type',
                'partial_payment_value', 'partial_payment_description',
                'get_partial_payment_amount', 'get_remaining_payment_amount'
            ),
            'classes': ('collapse',),
            'description': 'Configure advance payment requirements for this service'
        }),
        ('Service Details', {
            'fields': ('time_to_complete', 'is_active')
        }),
        ('SEO Settings', {
            'fields': ('meta_title', 'meta_description', 'og_image_url'),
            'classes': ('collapse',),
            'description': 'Search Engine Optimization and Social Media settings'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_current_price(self, obj):
        return f"₹{obj.get_current_price()}"
    get_current_price.short_description = 'Current Price'

    def get_discount_percentage(self, obj):
        percentage = obj.get_discount_percentage()
        return f"{percentage}%" if percentage > 0 else "No discount"
    get_discount_percentage.short_description = 'Discount %'

    def get_partial_payment_amount(self, obj):
        if not obj.requires_partial_payment:
            return "Not required"
        amount = obj.get_partial_payment_amount()
        return f"₹{amount}"
    get_partial_payment_amount.short_description = 'Partial Payment Amount'

    def get_remaining_payment_amount(self, obj):
        if not obj.requires_partial_payment:
            return "Full payment"
        amount = obj.get_remaining_payment_amount()
        return f"₹{amount}"
    get_remaining_payment_amount.short_description = 'Remaining Amount'


@admin.register(Discount)
class DiscountAdmin(admin.ModelAdmin):
    """
    Admin interface for Discount model.
    """
    list_display = [
        'name', 'code', 'discount_type', 'value',
        'applies_to_category', 'applies_to_service',
        'is_active', 'is_valid', 'start_date', 'end_date'
    ]
    list_filter = ['discount_type', 'is_active', 'start_date', 'end_date']
    search_fields = ['name', 'code', 'applies_to_category__name', 'applies_to_service__title']
    readonly_fields = ['created_at', 'updated_at', 'is_valid']

    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'discount_type', 'value')
        }),
        ('Validity', {
            'fields': ('start_date', 'end_date', 'is_active', 'is_valid')
        }),
        ('Applies To', {
            'fields': ('applies_to_category', 'applies_to_service'),
            'description': 'Select either a category OR a service, not both.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def is_valid(self, obj):
        return obj.is_valid()
    is_valid.boolean = True
    is_valid.short_description = 'Currently Valid'


# Customize admin site headers
admin.site.site_header = "Home Services - Catalogue Management"
admin.site.site_title = "Catalogue Admin"
admin.site.index_title = "Catalogue & Service Management"
