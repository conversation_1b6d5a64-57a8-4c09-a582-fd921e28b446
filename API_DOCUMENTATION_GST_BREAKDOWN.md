# 📋 API Documentation - GST Breakdown Integration

## 🔄 Changes Summary
- Added separate GST component fields (`cgst_amount`, `sgst_amount`, `igst_amount`, `ugst_amount`)
- Added `service_charge` field
- Added structured `tax_breakdown` array
- Enhanced both Cart and Order APIs

**Base URL:** `http://localhost:8000`

---

## 🛒 CART APIs - Updated with GST Breakdown

### 1. Get Cart Details
```http
GET http://localhost:8000/api/cart/
```

**Response (Updated):**
```json
{
  "id": 195,
  "user": null,
  "session_key": "9yzjifhcdve2n1cvpsh7qjg6axi19r5v",
  "created_at": "2025-06-16T15:42:44.747021+05:30",
  "updated_at": "2025-06-16T15:42:45.375310+05:30",
  "is_active": true,
  "sub_total": "780.00",
  "tax_amount": "140.40",
  "discount_amount": "0.00",
  "minimum_order_fee_applied": "0.00",
  "total_amount": "939.90",
  "coupon_code_applied": null,
  
  // 🆕 NEW GST BREAKDOWN FIELDS
  "cgst_amount": "70.20",
  "sgst_amount": "70.20", 
  "igst_amount": "0.00",
  "ugst_amount": "0.00",
  "service_charge": "19.50",
  "tax_breakdown": [
    {
      "type": "CGST",
      "rate": "9.00%",
      "amount": "70.20"
    },
    {
      "type": "SGST",
      "rate": "9.00%", 
      "amount": "70.20"
    },
    {
      "type": "Service Charge",
      "rate": "2.50%",
      "amount": "19.50"
    }
  ],
  
  "items": [...],
  "items_count": 1,
  "unique_services_count": 1
}
```

**Empty Cart Response:**
```json
{
  "id": null,
  "user": null,
  "session_key": null,
  "created_at": null,
  "updated_at": null,
  "is_active": true,
  "sub_total": "0.00",
  "tax_amount": "0.00",
  "discount_amount": "0.00",
  "minimum_order_fee_applied": "0.00",
  "total_amount": "0.00",
  "coupon_code_applied": null,
  
  // 🆕 NEW GST BREAKDOWN FIELDS (Empty)
  "cgst_amount": "0.00",
  "sgst_amount": "0.00",
  "igst_amount": "0.00", 
  "ugst_amount": "0.00",
  "service_charge": "0.00",
  "tax_breakdown": [],
  
  "items": [],
  "items_count": 0,
  "unique_services_count": 0
}
```

### 2. Add to Cart
```http
POST http://localhost:8000/api/cart/add/
Content-Type: application/json

{
  "service_id": 1,
  "quantity": 1
}
```

**Response:**
```json
{
  "success": true,
  "message": "Added 1 Basic Bathroom Cleaning Extra Large Size to cart",
  "cart": {
    // Same structure as Get Cart Details above with GST breakdown
    "sub_total": "780.00",
    "cgst_amount": "70.20",
    "sgst_amount": "70.20",
    "igst_amount": "0.00",
    "service_charge": "19.50",
    "tax_breakdown": [...],
    "total_amount": "939.90"
  }
}
```

### 3. Update Cart Item
```http
PUT http://localhost:8000/api/cart/items/{item_id}/
Content-Type: application/json

{
  "quantity": 2
}
```

### 4. Remove Cart Item
```http
DELETE http://localhost:8000/api/cart/items/{item_id}/
```

### 5. Clear Cart
```http
DELETE http://localhost:8000/api/cart/clear/
```

### 6. Apply Coupon
```http
POST http://localhost:8000/api/cart/apply-coupon/
Content-Type: application/json

{
  "coupon_code": "SAVE10"
}
```

---

## 📦 ORDER APIs - Updated with GST Breakdown

### 1. Get Order Details
```http
GET http://localhost:8000/api/orders/{order_id}/
```

**Response (Updated):**
```json
{
  "id": 123,
  "order_number": "ORD-2025-001",
  "customer": 45,
  "customer_name": "John Doe",
  "customer_mobile": "+919876543210",
  "status": "confirmed",
  "payment_status": "paid",
  "payment_method": "razorpay",
  "subtotal": "780.00",
  "tax_amount": "140.40",
  "discount_amount": "0.00",
  "total_amount": "939.90",
  
  // 🆕 NEW GST BREAKDOWN FIELDS
  "cgst_amount": "70.20",
  "sgst_amount": "70.20",
  "igst_amount": "0.00", 
  "ugst_amount": "0.00",
  "service_charge": "19.50",
  "tax_breakdown": [
    {
      "type": "CGST",
      "rate": "9%",
      "amount": "70.20"
    },
    {
      "type": "SGST",
      "rate": "9%",
      "amount": "70.20"
    },
    {
      "type": "Service Charge",
      "rate": "2.5%",
      "amount": "19.50"
    }
  ],
  
  "delivery_address": {...},
  "items": [...],
  "created_at": "2025-06-16T15:42:44.747021+05:30"
}
```

### 2. Create Order from Cart
```http
POST http://localhost:8000/api/orders/create/
Content-Type: application/json

{
  "cart_id": "195",
  "delivery_address": {
    "house_number": "123",
    "street_name": "Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400001"
  },
  "payment_method": "razorpay",
  "scheduled_date": "2025-06-20",
  "customer_notes": "Please call before arriving"
}
```

### 3. List User Orders
```http
GET http://localhost:8000/api/orders/
```

---

## 🏪 CATALOGUE APIs

### 1. Get Services
```http
GET http://localhost:8000/api/catalogue/services/
```

### 2. Get Service Details
```http
GET http://localhost:8000/api/catalogue/services/{service_id}/
```

### 3. Get Categories
```http
GET http://localhost:8000/api/catalogue/categories/
```

---

## 🔐 AUTHENTICATION APIs

### 1. Send OTP
```http
POST http://localhost:8000/api/auth/send-otp/
Content-Type: application/json

{
  "mobile_number": "+919876543210"
}
```

### 2. Verify OTP & Login
```http
POST http://localhost:8000/api/auth/verify-otp/
Content-Type: application/json

{
  "mobile_number": "+919876543210",
  "otp": "123456"
}
```

---

## 🧪 Quick Test Commands

### Test Cart API:
```bash
# Get empty cart
curl http://localhost:8000/api/cart/

# Add item to cart
curl -X POST http://localhost:8000/api/cart/add/ \
  -H "Content-Type: application/json" \
  -d '{"service_id": 1, "quantity": 1}'

# Get cart with GST breakdown
curl http://localhost:8000/api/cart/
```

### Test Services API:
```bash
# Get all services
curl http://localhost:8000/api/catalogue/services/

# Get specific service
curl http://localhost:8000/api/catalogue/services/1/
```

---

## 💻 Next.js Integration Examples

### 1. Environment Variables (.env.local)
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
```

### 2. TypeScript Types
```typescript
// types/cart.ts
interface TaxBreakdownItem {
  type: string;
  rate: string;
  amount: string;
}

interface Cart {
  id: number | null;
  sub_total: string;
  tax_amount: string;
  total_amount: string;

  // New GST fields
  cgst_amount: string;
  sgst_amount: string;
  igst_amount: string;
  ugst_amount: string;
  service_charge: string;
  tax_breakdown: TaxBreakdownItem[];

  items: CartItem[];
  items_count: number;
}
```

### 3. API Service Functions
```typescript
// services/api.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

export const cartAPI = {
  getCart: async () => {
    const response = await fetch(`${API_BASE_URL}/api/cart/`, {
      credentials: 'include'
    });
    return response.json();
  },

  addToCart: async (serviceId: number, quantity: number) => {
    const response = await fetch(`${API_BASE_URL}/api/cart/add/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        service_id: serviceId,
        quantity: quantity
      })
    });
    return response.json();
  },

  updateCartItem: async (itemId: number, quantity: number) => {
    const response = await fetch(`${API_BASE_URL}/api/cart/items/${itemId}/`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ quantity })
    });
    return response.json();
  },

  removeCartItem: async (itemId: number) => {
    const response = await fetch(`${API_BASE_URL}/api/cart/items/${itemId}/`, {
      method: 'DELETE',
      credentials: 'include'
    });
    return response.json();
  }
};

export const servicesAPI = {
  getServices: async () => {
    const response = await fetch(`${API_BASE_URL}/api/catalogue/services/`);
    return response.json();
  },

  getService: async (serviceId: number) => {
    const response = await fetch(`${API_BASE_URL}/api/catalogue/services/${serviceId}/`);
    return response.json();
  }
};
```

### 4. React Hook for Cart
```typescript
// hooks/useCart.ts
import { useState, useEffect } from 'react';
import { cartAPI } from '../services/api';

export const useCart = () => {
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(false);

  const fetchCart = async () => {
    setLoading(true);
    try {
      const cartData = await cartAPI.getCart();
      setCart(cartData);
    } catch (error) {
      console.error('Error fetching cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (serviceId: number, quantity: number = 1) => {
    try {
      const response = await cartAPI.addToCart(serviceId, quantity);
      if (response.success) {
        setCart(response.cart);
        return response;
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  };

  useEffect(() => {
    fetchCart();
  }, []);

  return {
    cart,
    loading,
    addToCart,
    fetchCart
  };
};
```

### 5. Cart Component with GST Breakdown
```tsx
// components/Cart.tsx
import React from 'react';
import { useCart } from '../hooks/useCart';

const Cart: React.FC = () => {
  const { cart, loading } = useCart();

  if (loading) return <div>Loading cart...</div>;
  if (!cart) return <div>No cart found</div>;

  return (
    <div className="cart">
      <h2>Your Cart</h2>

      {/* Cart Items */}
      {cart.items.map((item: any) => (
        <div key={item.id} className="cart-item">
          <h3>{item.service_title}</h3>
          <p>Quantity: {item.quantity}</p>
          <p>Price: ₹{item.total_price}</p>
        </div>
      ))}

      {/* GST Breakdown */}
      <div className="cart-summary">
        <div className="summary-row">
          <span>Subtotal:</span>
          <span>₹{cart.sub_total}</span>
        </div>

        {/* Show CGST & SGST separately */}
        {parseFloat(cart.cgst_amount) > 0 && (
          <div className="summary-row">
            <span>CGST (9%):</span>
            <span>₹{cart.cgst_amount}</span>
          </div>
        )}

        {parseFloat(cart.sgst_amount) > 0 && (
          <div className="summary-row">
            <span>SGST (9%):</span>
            <span>₹{cart.sgst_amount}</span>
          </div>
        )}

        {/* Show IGST if inter-state */}
        {parseFloat(cart.igst_amount) > 0 && (
          <div className="summary-row">
            <span>IGST (18%):</span>
            <span>₹{cart.igst_amount}</span>
          </div>
        )}

        {/* Service Charge */}
        {parseFloat(cart.service_charge) > 0 && (
          <div className="summary-row">
            <span>Service Charge (2.5%):</span>
            <span>₹{cart.service_charge}</span>
          </div>
        )}

        <div className="summary-row total">
          <span><strong>Total:</strong></span>
          <span><strong>₹{cart.total_amount}</strong></span>
        </div>
      </div>
    </div>
  );
};

export default Cart;
```

### 6. Invoice Component
```tsx
// components/Invoice.tsx
import React from 'react';

interface InvoiceProps {
  order: any;
}

const Invoice: React.FC<InvoiceProps> = ({ order }) => {
  return (
    <div className="invoice">
      <div className="invoice-header">
        <h2>Tax Invoice</h2>
        <p>Order: {order.order_number}</p>
      </div>

      {/* Items */}
      <table className="invoice-items">
        <thead>
          <tr>
            <th>Service</th>
            <th>Qty</th>
            <th>Rate</th>
            <th>Amount</th>
          </tr>
        </thead>
        <tbody>
          {order.items.map((item: any) => (
            <tr key={item.id}>
              <td>{item.service_title}</td>
              <td>{item.quantity}</td>
              <td>₹{item.unit_price}</td>
              <td>₹{item.total_price}</td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Tax Summary */}
      <div className="tax-summary">
        <div className="tax-row">
          <span>Subtotal:</span>
          <span>₹{order.subtotal}</span>
        </div>

        {/* GST Breakdown */}
        {order.tax_breakdown.map((tax: any, index: number) => (
          <div key={index} className="tax-row">
            <span>{tax.type} ({tax.rate}):</span>
            <span>₹{tax.amount}</span>
          </div>
        ))}

        <div className="tax-row total">
          <span><strong>Total Amount:</strong></span>
          <span><strong>₹{order.total_amount}</strong></span>
        </div>
      </div>
    </div>
  );
};

export default Invoice;
```

### 7. GST Display Logic
```typescript
// utils/taxUtils.ts
export const formatTaxBreakdown = (taxBreakdown: TaxBreakdownItem[]) => {
  return taxBreakdown.map(tax => ({
    ...tax,
    formattedAmount: `₹${parseFloat(tax.amount).toFixed(2)}`
  }));
};

export const getTotalGST = (cart: Cart): number => {
  return parseFloat(cart.cgst_amount) +
         parseFloat(cart.sgst_amount) +
         parseFloat(cart.igst_amount) +
         parseFloat(cart.ugst_amount);
};

export const isInterStateTransaction = (cart: Cart): boolean => {
  return parseFloat(cart.igst_amount) > 0;
};

// Display logic for different transaction types
export const renderGSTBreakdown = (cart: Cart) => {
  const hasIGST = parseFloat(cart.igst_amount) > 0;
  const hasCGSTSGST = parseFloat(cart.cgst_amount) > 0 || parseFloat(cart.sgst_amount) > 0;

  if (hasIGST) {
    // Inter-state transaction - show IGST only
    return {
      type: 'inter-state',
      taxes: [
        { type: 'IGST', rate: '18%', amount: cart.igst_amount },
        { type: 'Service Charge', rate: '2.5%', amount: cart.service_charge }
      ]
    };
  } else if (hasCGSTSGST) {
    // Intra-state transaction - show CGST + SGST
    return {
      type: 'intra-state',
      taxes: [
        { type: 'CGST', rate: '9%', amount: cart.cgst_amount },
        { type: 'SGST', rate: '9%', amount: cart.sgst_amount },
        { type: 'Service Charge', rate: '2.5%', amount: cart.service_charge }
      ]
    };
  }

  return { type: 'no-tax', taxes: [] };
};
```

---

## 🎯 Key Changes for Next.js

### 1. Update Type Definitions
Add new GST fields to your TypeScript interfaces

### 2. Update Cart Components
Modify cart summary to show separate CGST/SGST

### 3. Update Checkout Flow
Include GST breakdown in checkout summary

### 4. Update Invoice Generation
Use structured tax_breakdown for GST-compliant invoices

### 5. Update Order History
Show GST breakdown in order details

---

## 📊 GST Compliance Features

1. **✅ Separate CGST/SGST**: Required for Indian GST invoices
2. **✅ Single IGST Entry**: Correct for inter-state transactions
3. **✅ Rate Display**: Shows actual tax rates applied
4. **✅ Amount Breakdown**: Individual amounts for each component
5. **✅ Service Charge**: Platform fee shown separately

---

## 🚀 Next Steps

1. **Set up environment variables** with the Django backend URL
2. **Create API service functions** using the working endpoints above
3. **Implement cart components** with GST breakdown display
4. **Test the integration** using the provided curl commands first
5. **Handle authentication** with session cookies or JWT tokens

All these endpoints are working and tested! The GST breakdown will be included in all cart and order responses. 🎉
