from django.db import models
from decimal import Decimal
from django.utils import timezone
from django.core.exceptions import ValidationError


class TaxCategory(models.Model):
    """
    Tax categories for different types of services (e.g., Home Services, Professional Services)
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Tax Category"
        verbose_name_plural = "Tax Categories"
        ordering = ['name']

    def __str__(self):
        return self.name


class GSTRate(models.Model):
    """
    GST rates configuration for different service categories
    """
    GST_TYPE_CHOICES = [
        ('CGST', 'Central GST'),
        ('SGST', 'State GST'),
        ('IGST', 'Integrated GST'),
        ('UGST', 'Union Territory GST'),
    ]

    tax_category = models.ForeignKey(
        TaxCategory,
        on_delete=models.CASCADE,
        related_name='gst_rates'
    )
    gst_type = models.CharField(max_length=4, choices=GST_TYPE_CHOICES)
    rate_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        help_text="GST rate as percentage (e.g., 18.00 for 18%)"
    )
    hsn_code = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        help_text="HSN/SAC code for services"
    )
    effective_from = models.DateTimeField(default=timezone.now)
    effective_until = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GST Rate"
        verbose_name_plural = "GST Rates"
        ordering = ['-effective_from']
        unique_together = ['tax_category', 'gst_type', 'effective_from']

    def clean(self):
        if self.effective_until and self.effective_until <= self.effective_from:
            raise ValidationError("Effective until date must be after effective from date.")

    def is_currently_effective(self):
        """Check if this GST rate is currently effective"""
        now = timezone.now()
        if not self.is_active:
            return False
        if self.effective_from > now:
            return False
        if self.effective_until and self.effective_until <= now:
            return False
        return True

    def __str__(self):
        return f"{self.tax_category.name} - {self.gst_type} ({self.rate_percentage}%)"


class TaxConfiguration(models.Model):
    """
    Global tax configuration settings
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    
    # Tax exemption thresholds
    tax_exemption_threshold = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Orders below this amount are tax exempt"
    )
    
    # Default tax category
    default_tax_category = models.ForeignKey(
        TaxCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Default tax category for services without specific category"
    )
    
    # Tax calculation settings
    round_tax_to_nearest_paisa = models.BooleanField(
        default=True,
        help_text="Round tax amounts to nearest paisa (0.01)"
    )
    
    # Service charge settings
    service_charge_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Platform service charge percentage"
    )
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Tax Configuration"
        verbose_name_plural = "Tax Configurations"

    def __str__(self):
        return self.name


class TaxCalculation(models.Model):
    """
    Store tax calculations for audit and reference
    """
    # Reference to cart or order (using generic approach to avoid cross-database issues)
    reference_type = models.CharField(
        max_length=20,
        choices=[('cart', 'Cart'), ('order', 'Order')],
        default='cart'
    )
    reference_id = models.PositiveIntegerField()
    
    # Tax breakdown
    subtotal = models.DecimalField(max_digits=10, decimal_places=2)
    cgst_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    sgst_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    igst_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    ugst_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_tax = models.DecimalField(max_digits=10, decimal_places=2)
    service_charge = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Configuration used
    tax_configuration = models.ForeignKey(
        TaxConfiguration,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    
    # Metadata
    calculated_at = models.DateTimeField(auto_now_add=True)
    calculation_details = models.JSONField(
        blank=True,
        null=True,
        help_text="Detailed breakdown of tax calculation"
    )

    class Meta:
        verbose_name = "Tax Calculation"
        verbose_name_plural = "Tax Calculations"
        ordering = ['-calculated_at']
        indexes = [
            models.Index(fields=['reference_type', 'reference_id']),
        ]

    def __str__(self):
        return f"Tax calculation for {self.reference_type} {self.reference_id}"
