from rest_framework import generics, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON>ted, IsAdminUser, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count
from decimal import Decimal, InvalidOperation
from .models import Category, Service, Discount
from .serializers import (
    CategorySerializer, CategoryListSerializer, CategoryTreeSerializer,
    CategoryCreateUpdateSerializer, ServiceSerializer, ServiceListSerializer,
    ServiceCreateUpdateSerializer, DiscountSerializer
)


class CategoryListCreateView(generics.ListCreateAPIView):
    """
    List all categories or create a new category.
    GET: Public access for listing
    POST: Admin only for creation
    """
    queryset = Category.objects.filter(is_active=True)
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['parent', 'level']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CategoryCreateUpdateSerializer
        return CategoryListSerializer

    def get_permissions(self):
        if self.request.method == 'POST':
            return [IsAdminUser()]
        return [AllowAny()]


class CategoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a category.
    GET: Public access
    PUT/PATCH/DELETE: Admin only
    """
    queryset = Category.objects.all()
    lookup_field = 'slug'

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return CategoryCreateUpdateSerializer
        return CategorySerializer

    def get_permissions(self):
        if self.request.method == 'GET':
            return [AllowAny()]
        return [IsAdminUser()]

    def perform_destroy(self, instance):
        # Soft delete - mark as inactive instead of actual deletion
        instance.is_active = False
        instance.save()


class CategoryTreeView(generics.ListAPIView):
    """
    Get hierarchical tree structure of all categories.
    """
    serializer_class = CategoryTreeSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        return Category.objects.filter(is_active=True, level=0)  # Root categories only


class ServiceListCreateView(generics.ListCreateAPIView):
    """
    List all services or create a new service.
    GET: Public access for listing
    POST: Admin only for creation
    """
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'category__parent']
    search_fields = ['title', 'description']
    ordering_fields = ['title', 'base_price', 'created_at']
    ordering = ['title']

    def get_queryset(self):
        queryset = Service.objects.filter(is_active=True).select_related('category')

        # Filter by category slug if provided (includes subcategories)
        category_slug = self.request.query_params.get('category_slug')
        if category_slug:
            try:
                # Get the main category
                category_obj = Category.objects.get(slug=category_slug, is_active=True)
                # Get all descendant categories (including self)
                descendant_categories = category_obj.get_descendants(include_self=True)
                # Filter services by these categories
                queryset = queryset.filter(category__in=descendant_categories)
            except Category.DoesNotExist:
                # If category_slug is invalid, return empty queryset
                return Service.objects.none()

        # Filter by price range with proper type conversion
        min_price_str = self.request.query_params.get('min_price')
        max_price_str = self.request.query_params.get('max_price')

        if min_price_str:
            try:
                min_price = Decimal(min_price_str)
                queryset = queryset.filter(base_price__gte=min_price)
            except (ValueError, InvalidOperation):
                # Handle invalid price format - ignore filter
                pass

        if max_price_str:
            try:
                max_price = Decimal(max_price_str)
                queryset = queryset.filter(base_price__lte=max_price)
            except (ValueError, InvalidOperation):
                # Handle invalid price format - ignore filter
                pass

        return queryset

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ServiceCreateUpdateSerializer
        return ServiceListSerializer

    def get_permissions(self):
        if self.request.method == 'POST':
            return [IsAdminUser()]
        return [AllowAny()]


class ServiceDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a service.
    GET: Public access
    PUT/PATCH/DELETE: Admin only
    """
    queryset = Service.objects.all()
    lookup_field = 'slug'

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return ServiceCreateUpdateSerializer
        return ServiceSerializer

    def get_permissions(self):
        if self.request.method == 'GET':
            return [AllowAny()]
        return [IsAdminUser()]

    def perform_destroy(self, instance):
        # Soft delete - mark as inactive instead of actual deletion
        instance.is_active = False
        instance.save()


class DiscountListCreateView(generics.ListCreateAPIView):
    """
    List all discounts or create a new discount.
    Admin only access.
    """
    queryset = Discount.objects.all().select_related('applies_to_category', 'applies_to_service')
    serializer_class = DiscountSerializer
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['discount_type', 'is_active', 'applies_to_category', 'applies_to_service']
    search_fields = ['name', 'code']
    ordering = ['-created_at']


class DiscountDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a discount.
    Admin only access.
    """
    queryset = Discount.objects.all()
    serializer_class = DiscountSerializer
    permission_classes = [IsAdminUser]


@api_view(['GET'])
@permission_classes([AllowAny])
def category_services(request, category_slug):
    """
    Get all services for a specific category including subcategories.
    """
    category = get_object_or_404(Category, slug=category_slug, is_active=True)

    # Get all descendant categories (subcategories)
    descendant_categories = category.get_descendants(include_self=True)

    # Get all services from this category and its subcategories
    services = Service.objects.filter(
        category__in=descendant_categories,
        is_active=True
    ).select_related('category')

    # Apply filters with proper type conversion
    min_price_str = request.query_params.get('min_price')
    max_price_str = request.query_params.get('max_price')

    if min_price_str:
        try:
            min_price = Decimal(min_price_str)
            services = services.filter(base_price__gte=min_price)
        except (ValueError, InvalidOperation):
            # Handle invalid price format - ignore filter
            pass

    if max_price_str:
        try:
            max_price = Decimal(max_price_str)
            services = services.filter(base_price__lte=max_price)
        except (ValueError, InvalidOperation):
            # Handle invalid price format - ignore filter
            pass

    # Apply search
    search = request.query_params.get('search')
    if search:
        services = services.filter(
            Q(title__icontains=search) | Q(description__icontains=search)
        )

    # Apply ordering
    ordering = request.query_params.get('ordering', 'title')
    services = services.order_by(ordering)

    serializer = ServiceListSerializer(services, many=True)

    return Response({
        'category': CategoryListSerializer(category).data,
        'services': serializer.data,
        'total_services': services.count()
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def search_services(request):
    """
    Global search across all services.
    """
    query = request.query_params.get('q', '')
    category_slug = request.query_params.get('category')
    min_price_str = request.query_params.get('min_price')
    max_price_str = request.query_params.get('max_price')

    services = Service.objects.filter(is_active=True).select_related('category')

    if query:
        services = services.filter(
            Q(title__icontains=query) |
            Q(description__icontains=query) |
            Q(category__name__icontains=query)
        )

    if category_slug:
        category = get_object_or_404(Category, slug=category_slug, is_active=True)
        descendant_categories = category.get_descendants(include_self=True)
        services = services.filter(category__in=descendant_categories)

    # Filter by price range with proper type conversion
    if min_price_str:
        try:
            min_price = Decimal(min_price_str)
            services = services.filter(base_price__gte=min_price)
        except (ValueError, InvalidOperation):
            # Handle invalid price format - ignore filter
            pass

    if max_price_str:
        try:
            max_price = Decimal(max_price_str)
            services = services.filter(base_price__lte=max_price)
        except (ValueError, InvalidOperation):
            # Handle invalid price format - ignore filter
            pass

    # Apply ordering
    ordering = request.query_params.get('ordering', 'title')
    services = services.order_by(ordering)

    serializer = ServiceListSerializer(services, many=True)

    return Response({
        'query': query,
        'services': serializer.data,
        'total_results': services.count()
    })
