from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from authentication.models import FailedLoginAttempt
from authentication.utils import RateLimitService

User = get_user_model()


class Command(BaseCommand):
    help = 'Clear failed login attempts to allow users to request OTP again'

    def add_arguments(self, parser):
        parser.add_argument(
            '--mobile',
            type=str,
            help='Clear failed attempts for specific mobile number (format: +91XXXXXXXXXX)',
        )
        parser.add_argument(
            '--email',
            type=str,
            help='Clear failed attempts for specific email address',
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='Clear failed attempts for specific user ID',
        )
        parser.add_argument(
            '--ip',
            type=str,
            help='Clear failed attempts from specific IP address',
        )
        parser.add_argument(
            '--older-than',
            type=int,
            help='Clear attempts older than X hours (default: clear all)',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Clear ALL failed login attempts',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleared without actually clearing',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS(
                f'🧹 Clearing Failed Login Attempts (dry_run={dry_run})...'
            )
        )

        # Build query based on options
        queryset = FailedLoginAttempt.objects.all()
        description = "all failed login attempts"

        if options['mobile']:
            mobile = options['mobile']
            queryset = queryset.filter(mobile_number=mobile)
            description = f"failed attempts for mobile {mobile}"
            
        elif options['email']:
            email = options['email']
            queryset = queryset.filter(email=email)
            description = f"failed attempts for email {email}"
            
        elif options['user_id']:
            user_id = options['user_id']
            queryset = queryset.filter(user_id=user_id)
            description = f"failed attempts for user ID {user_id}"
            
        elif options['ip']:
            ip = options['ip']
            queryset = queryset.filter(ip_address=ip)
            description = f"failed attempts from IP {ip}"
            
        elif not options['all']:
            self.stdout.write(
                self.style.ERROR(
                    'Please specify --mobile, --email, --user-id, --ip, or --all'
                )
            )
            return

        # Filter by age if specified
        if options['older_than']:
            hours = options['older_than']
            cutoff_time = timezone.now() - timedelta(hours=hours)
            queryset = queryset.filter(timestamp__lt=cutoff_time)
            description += f" older than {hours} hours"

        # Get count and details
        total_count = queryset.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.WARNING(f'No failed login attempts found matching criteria.')
            )
            return

        # Show what will be cleared
        self.stdout.write(f'Found {total_count} {description}')
        
        if dry_run:
            # Show sample records
            sample_attempts = queryset[:5]
            for attempt in sample_attempts:
                identifier = attempt.email or attempt.mobile_number or 'Unknown'
                self.stdout.write(
                    f'  Would clear: {identifier} from {attempt.ip_address} at {attempt.timestamp}'
                )
            if total_count > 5:
                self.stdout.write(f'  ... and {total_count - 5} more')
        else:
            # Get affected users for rate limit reset
            affected_users = set()
            for attempt in queryset:
                if attempt.user_id:
                    affected_users.add(attempt.user_id)

            # Clear the attempts
            queryset.delete()
            self.stdout.write(
                self.style.SUCCESS(f'✅ Cleared {total_count} failed login attempts')
            )

            # Reset rate limits for affected users
            if affected_users:
                self.stdout.write(f'🔄 Resetting rate limits for {len(affected_users)} users...')
                
                for user_id in affected_users:
                    try:
                        user = User.objects.get(id=user_id)
                        cleared_keys = RateLimitService.clear_user_rate_limits(user)
                        self.stdout.write(f"  Cleared rate limits for user {user_id}: {len(cleared_keys)} keys")
                    except User.DoesNotExist:
                        pass
                
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Reset rate limits for affected users')
                )

        self.stdout.write(
            self.style.SUCCESS('🎉 Operation completed!')
        )

    def show_usage_examples(self):
        """Show usage examples"""
        examples = [
            "# Clear attempts for specific mobile number:",
            "python manage.py clear_failed_attempts --mobile +919876543210",
            "",
            "# Clear attempts for specific email:",
            "python manage.py clear_failed_attempts --email <EMAIL>",
            "",
            "# Clear attempts for specific user ID:",
            "python manage.py clear_failed_attempts --user-id 123",
            "",
            "# Clear attempts from specific IP:",
            "python manage.py clear_failed_attempts --ip *************",
            "",
            "# Clear all attempts older than 24 hours:",
            "python manage.py clear_failed_attempts --all --older-than 24",
            "",
            "# Clear ALL attempts (use with caution):",
            "python manage.py clear_failed_attempts --all",
            "",
            "# Dry run to see what would be cleared:",
            "python manage.py clear_failed_attempts --mobile +919876543210 --dry-run",
        ]
        
        for example in examples:
            self.stdout.write(example)
