#!/usr/bin/env python
"""
Test script to verify the cart API is working after the database changes.
"""
import requests
import json

def test_add_to_cart():
    """Test adding a service to cart"""
    url = "http://localhost:8000/api/cart/add/"
    
    # Test data
    data = {
        "service_id": 1,
        "quantity": 1
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("🧪 Testing Add to Cart API...")
        print(f"URL: {url}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, json=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 201:
            print("✅ SUCCESS: Add to cart API is working!")
            return True
        else:
            print("❌ FAILED: Add to cart API returned error")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to Django server. Is it running on port 8000?")
        return False
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

def test_get_cart():
    """Test getting cart details"""
    url = "http://localhost:8000/api/cart/"
    
    try:
        print("\n🧪 Testing Get Cart API...")
        print(f"URL: {url}")
        
        response = requests.get(url)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Get cart API is working!")
            return True
        else:
            print("❌ FAILED: Get cart API returned error")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to Django server. Is it running on port 8000?")
        return False
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Cart API after database changes...")
    print("=" * 50)
    
    # Test add to cart
    add_success = test_add_to_cart()
    
    # Test get cart
    get_success = test_get_cart()
    
    print("\n" + "=" * 50)
    if add_success and get_success:
        print("🎉 ALL TESTS PASSED! Cart API is working correctly.")
    else:
        print("⚠️  SOME TESTS FAILED. Check the errors above.")
