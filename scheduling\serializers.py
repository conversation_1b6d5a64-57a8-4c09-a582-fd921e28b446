from rest_framework import serializers
from django.utils import timezone
from datetime import datetime, timedelta
from .models import (
    SlotConfiguration, WorkingShift, HolidaySchedule,
    TimeSlot, SlotBlockage, SlotBooking
)


class SlotConfigurationSerializer(serializers.ModelSerializer):
    """Serializer for SlotConfiguration model."""
    
    class Meta:
        model = SlotConfiguration
        fields = [
            'id', 'name', 'slot_interval_minutes', 'buffer_time_minutes',
            'advance_booking_days', 'same_day_booking_cutoff_hours',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class WorkingShiftSerializer(serializers.ModelSerializer):
    """Serializer for WorkingShift model."""
    weekday_display = serializers.CharField(source='get_weekday_display', read_only=True)
    
    class Meta:
        model = WorkingShift
        fields = [
            'id', 'configuration', 'weekday', 'weekday_display',
            'start_time', 'end_time', 'is_working_day', 'max_bookings_per_slot'
        ]
    
    def validate(self, data):
        if data.get('is_working_day') and data.get('start_time') and data.get('end_time'):
            if data['start_time'] >= data['end_time']:
                raise serializers.ValidationError("Start time must be before end time")
        return data


class HolidayScheduleSerializer(serializers.ModelSerializer):
    """Serializer for HolidaySchedule model."""
    
    class Meta:
        model = HolidaySchedule
        fields = [
            'id', 'name', 'date', 'holiday_type', 'is_recurring',
            'description', 'is_active', 'created_at'
        ]
        read_only_fields = ['created_at']
    
    def validate_date(self, value):
        if value < timezone.now().date():
            raise serializers.ValidationError("Holiday date cannot be in the past")
        return value


class TimeSlotSerializer(serializers.ModelSerializer):
    """Serializer for TimeSlot model."""
    is_available = serializers.BooleanField(read_only=True)
    remaining_capacity = serializers.IntegerField(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = TimeSlot
        fields = [
            'id', 'configuration', 'date', 'start_time', 'end_time',
            'status', 'status_display', 'current_bookings', 'max_bookings',
            'is_available', 'remaining_capacity', 'booked_orders',
            'blocked_reason', 'blocked_by', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'current_bookings', 'booked_orders',
            'created_at', 'updated_at'
        ]


class TimeSlotListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing time slots."""
    is_available = serializers.BooleanField(read_only=True)
    remaining_capacity = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = TimeSlot
        fields = [
            'id', 'date', 'start_time', 'end_time', 'status',
            'current_bookings', 'max_bookings', 'is_available',
            'remaining_capacity'
        ]


class SlotBlockageSerializer(serializers.ModelSerializer):
    """Serializer for SlotBlockage model."""
    
    class Meta:
        model = SlotBlockage
        fields = [
            'id', 'name', 'blockage_type', 'start_date', 'end_date',
            'start_time', 'end_time', 'is_recurring', 'recurrence_pattern',
            'recurrence_end_date', 'reason', 'blocked_by', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        # Validate date range
        if data.get('end_date') and data.get('start_date'):
            if data['end_date'] < data['start_date']:
                raise serializers.ValidationError("End date must be after start date")
        
        # Validate time range
        if data.get('end_time') and data.get('start_time'):
            if data['end_time'] <= data['start_time']:
                raise serializers.ValidationError("End time must be after start time")
        
        # Validate recurrence settings
        if data.get('is_recurring'):
            if not data.get('recurrence_pattern'):
                raise serializers.ValidationError(
                    "Recurrence pattern is required for recurring blockages"
                )
        
        return data


class SlotBookingSerializer(serializers.ModelSerializer):
    """Serializer for SlotBooking model."""
    time_slot_info = serializers.SerializerMethodField()
    booking_status_display = serializers.CharField(source='get_booking_status_display', read_only=True)
    
    class Meta:
        model = SlotBooking
        fields = [
            'id', 'time_slot', 'time_slot_info', 'order_id',
            'customer_mobile', 'customer_name', 'booking_status',
            'booking_status_display', 'quantity', 'service_names',
            'booked_at', 'confirmed_at', 'cancelled_at', 'completed_at',
            'customer_notes', 'admin_notes'
        ]
        read_only_fields = [
            'id', 'booked_at', 'confirmed_at', 'cancelled_at', 'completed_at'
        ]
    
    def get_time_slot_info(self, obj):
        return {
            'date': obj.time_slot.date,
            'start_time': obj.time_slot.start_time,
            'end_time': obj.time_slot.end_time,
            'status': obj.time_slot.status
        }


class CreateBookingSerializer(serializers.Serializer):
    """Serializer for creating slot bookings."""
    time_slot_id = serializers.UUIDField()
    order_id = serializers.CharField(max_length=100)
    customer_mobile = serializers.CharField(max_length=15)
    customer_name = serializers.CharField(max_length=100)
    quantity = serializers.IntegerField(min_value=1, default=1)
    service_names = serializers.ListField(
        child=serializers.CharField(max_length=200),
        allow_empty=False
    )
    customer_notes = serializers.CharField(max_length=500, required=False, allow_blank=True)
    
    def validate_time_slot_id(self, value):
        try:
            time_slot = TimeSlot.objects.get(id=value)
            if not time_slot.is_available:
                raise serializers.ValidationError("Time slot is not available")
            return value
        except TimeSlot.DoesNotExist:
            raise serializers.ValidationError("Time slot does not exist")
    
    def validate(self, data):
        time_slot = TimeSlot.objects.get(id=data['time_slot_id'])
        quantity = data.get('quantity', 1)
        
        if not time_slot.can_book(quantity):
            raise serializers.ValidationError(
                f"Cannot book {quantity} slots. Only {time_slot.remaining_capacity} available."
            )
        
        return data


class AvailableSlotsRequestSerializer(serializers.Serializer):
    """Serializer for requesting available slots."""
    date = serializers.DateField()
    service_duration_minutes = serializers.IntegerField(min_value=15, required=False)
    
    def validate_date(self, value):
        if value < timezone.now().date():
            raise serializers.ValidationError("Cannot request slots for past dates")
        return value


class SlotGenerationSerializer(serializers.Serializer):
    """Serializer for generating time slots."""
    configuration_id = serializers.IntegerField()
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    
    def validate(self, data):
        if data['end_date'] < data['start_date']:
            raise serializers.ValidationError("End date must be after start date")
        
        # Limit generation to reasonable range
        date_diff = (data['end_date'] - data['start_date']).days
        if date_diff > 90:
            raise serializers.ValidationError("Cannot generate slots for more than 90 days at once")
        
        try:
            SlotConfiguration.objects.get(id=data['configuration_id'], is_active=True)
        except SlotConfiguration.DoesNotExist:
            raise serializers.ValidationError("Invalid or inactive slot configuration")
        
        return data


class BulkSlotUpdateSerializer(serializers.Serializer):
    """Serializer for bulk slot updates."""
    slot_ids = serializers.ListField(
        child=serializers.UUIDField(),
        allow_empty=False,
        max_length=100  # Limit bulk operations
    )
    action = serializers.ChoiceField(choices=[
        ('block', 'Block slots'),
        ('unblock', 'Unblock slots'),
        ('maintenance', 'Set maintenance'),
        ('available', 'Make available')
    ])
    reason = serializers.CharField(max_length=200, required=False)
    
    def validate_slot_ids(self, value):
        existing_slots = TimeSlot.objects.filter(id__in=value).count()
        if existing_slots != len(value):
            raise serializers.ValidationError("Some slot IDs are invalid")
        return value
