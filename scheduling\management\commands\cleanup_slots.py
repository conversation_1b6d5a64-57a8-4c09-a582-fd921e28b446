from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from datetime import timedelta


class Command(BaseCommand):
    help = 'Clean up old time slots to maintain database performance'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days-to-keep',
            type=int,
            default=30,
            help='Number of days of past slots to keep (default: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )
    
    def handle(self, *args, **options):
        days_to_keep = options['days_to_keep']
        dry_run = options['dry_run']
        
        try:
            from scheduling.services import SlotMaintenanceService
            from scheduling.models import TimeSlot
            
            cutoff_date = timezone.now().date() - timedelta(days=days_to_keep)
            
            # Count slots to be deleted
            slots_to_delete = TimeSlot.objects.filter(
                date__lt=cutoff_date,
                status__in=['available', 'blocked']
            )
            
            count = slots_to_delete.count()
            
            if count == 0:
                self.stdout.write(
                    self.style.SUCCESS('No old slots found to clean up')
                )
                return
            
            self.stdout.write(f'Found {count} old slots to clean up')
            self.stdout.write(f'Cutoff date: {cutoff_date}')
            
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(
                        f'DRY RUN: Would delete {count} slots (use without --dry-run to actually delete)'
                    )
                )
                return
            
            # Perform cleanup
            deleted_count = SlotMaintenanceService.cleanup_past_slots(days_to_keep)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully cleaned up {deleted_count} old time slots'
                )
            )
            
        except Exception as e:
            raise CommandError(f'Error during cleanup: {str(e)}')
