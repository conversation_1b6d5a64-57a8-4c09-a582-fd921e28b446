from django.db import models
from django.conf import settings
from django.core.validators import RegexValidator
from decimal import Decimal
from django.utils import timezone
from catalogue.models import Service, Category


class ProviderProfile(models.Model):
    """
    Extended profile for service providers with business information.
    """
    VERIFICATION_STATUS_CHOICES = [
        ('pending', 'Pending Verification'),
        ('under_review', 'Under Review'),
        ('verified', 'Verified'),
        ('rejected', 'Rejected'),
        ('suspended', 'Suspended'),
    ]

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='provider_profile',
        limit_choices_to={'user_type': 'provider'},
        db_constraint=False  # Cross-database foreign key
    )
    
    # Business Information
    business_name = models.CharField(max_length=255, blank=True, null=True)
    business_type = models.CharField(
        max_length=50,
        choices=[
            ('individual', 'Individual'),
            ('partnership', 'Partnership'),
            ('company', 'Company'),
            ('llp', 'Limited Liability Partnership'),
        ],
        default='individual'
    )
    
    # Experience and Skills
    years_of_experience = models.PositiveIntegerField(default=0)
    specializations = models.ManyToManyField(
        Category,
        blank=True,
        help_text="Categories this provider specializes in",
        db_constraint=False  # Cross-database foreign key
    )
    services_offered = models.ManyToManyField(
        Service,
        blank=True,
        help_text="Specific services this provider offers",
        db_constraint=False  # Cross-database foreign key
    )
    
    # Location and Availability
    service_areas = models.JSONField(
        default=list,
        help_text="List of areas where provider offers services"
    )
    working_hours = models.JSONField(
        default=dict,
        help_text="Working hours for each day of the week"
    )
    
    # Verification Status
    verification_status = models.CharField(
        max_length=20,
        choices=VERIFICATION_STATUS_CHOICES,
        default='pending'
    )
    verified_at = models.DateTimeField(null=True, blank=True)
    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_providers',
        limit_choices_to={'user_type': 'staff'},
        db_constraint=False  # Cross-database foreign key
    )
    
    # Rating and Performance
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_reviews = models.PositiveIntegerField(default=0)
    total_orders_completed = models.PositiveIntegerField(default=0)
    
    # Financial Information
    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('15.00'),
        help_text="Commission percentage charged by platform"
    )
    
    # Profile Settings
    is_available = models.BooleanField(default=True)
    accepts_new_orders = models.BooleanField(default=True)
    profile_description = models.TextField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def update_rating(self):
        """Update average rating based on reviews"""
        from django.db.models import Avg
        # This would be implemented when review system is added
        pass

    def can_accept_orders(self):
        """Check if provider can accept new orders"""
        return (
            self.verification_status == 'verified' and
            self.is_available and
            self.accepts_new_orders and
            self.user.is_active
        )

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.mobile} - {self.business_name or 'Provider'}"


class ProviderDocument(models.Model):
    """
    Store provider verification documents.
    """
    DOCUMENT_TYPE_CHOICES = [
        ('aadhaar', 'Aadhaar Card'),
        ('pan', 'PAN Card'),
        ('bank_statement', 'Bank Statement'),
        ('address_proof', 'Address Proof'),
        ('business_license', 'Business License'),
        ('experience_certificate', 'Experience Certificate'),
        ('other', 'Other'),
    ]
    
    VERIFICATION_STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired'),
    ]

    provider = models.ForeignKey(
        ProviderProfile,
        on_delete=models.CASCADE,
        related_name='documents'
    )
    
    document_type = models.CharField(max_length=30, choices=DOCUMENT_TYPE_CHOICES)
    document_file = models.FileField(upload_to='provider_documents/')
    document_number = models.CharField(max_length=100, blank=True, null=True)
    
    verification_status = models.CharField(
        max_length=20,
        choices=VERIFICATION_STATUS_CHOICES,
        default='pending'
    )
    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_documents',
        limit_choices_to={'user_type': 'staff'},
        db_constraint=False  # Cross-database foreign key
    )
    verified_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, null=True)
    
    # Document validity
    issued_date = models.DateField(null=True, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    
    uploaded_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('provider', 'document_type')
        ordering = ['-uploaded_at']

    def is_expired(self):
        """Check if document is expired"""
        if self.expiry_date:
            return self.expiry_date < timezone.now().date()
        return False

    def __str__(self):
        return f"{self.provider.user.mobile} - {self.get_document_type_display()}"


class ProviderBankDetails(models.Model):
    """
    Store provider bank account details for payments.
    """
    provider = models.OneToOneField(
        ProviderProfile,
        on_delete=models.CASCADE,
        related_name='bank_details'
    )
    
    # Bank Account Information
    account_holder_name = models.CharField(max_length=255)
    account_number = models.CharField(max_length=20)
    ifsc_code = models.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r'^[A-Z]{4}0[A-Z0-9]{6}$',
                message='Enter a valid IFSC code'
            )
        ]
    )
    bank_name = models.CharField(max_length=255)
    branch_name = models.CharField(max_length=255)
    
    # UPI Information
    upi_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        validators=[
            RegexValidator(
                regex=r'^[\w\.-]+@[\w\.-]+$',
                message='Enter a valid UPI ID'
            )
        ]
    )
    
    # Verification
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_bank_details',
        limit_choices_to={'user_type': 'staff'},
        db_constraint=False  # Cross-database foreign key
    )
    verified_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.provider.user.mobile} - {self.bank_name}"


class ProviderPayoutRequest(models.Model):
    """
    Track payout requests from providers.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
        ('failed', 'Failed'),
    ]

    provider = models.ForeignKey(
        ProviderProfile,
        on_delete=models.CASCADE,
        related_name='payout_requests'
    )
    
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Processing details
    processed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_payouts',
        limit_choices_to={'user_type': 'staff'},
        db_constraint=False  # Cross-database foreign key
    )
    processed_at = models.DateTimeField(null=True, blank=True)
    
    # Transaction details
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    gateway_response = models.JSONField(blank=True, null=True)
    
    # Notes
    provider_notes = models.TextField(blank=True, null=True)
    admin_notes = models.TextField(blank=True, null=True)
    rejection_reason = models.TextField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Payout ₹{self.amount} - {self.provider.user.mobile}"


class ProviderAvailability(models.Model):
    """
    Track provider availability and time slots.
    """
    provider = models.ForeignKey(
        ProviderProfile,
        on_delete=models.CASCADE,
        related_name='availability_slots'
    )
    
    date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_available = models.BooleanField(default=True)
    
    # Booking information
    is_booked = models.BooleanField(default=False)
    booked_order_id = models.CharField(max_length=50, blank=True, null=True)
    
    notes = models.TextField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('provider', 'date', 'start_time')
        ordering = ['date', 'start_time']

    def __str__(self):
        return f"{self.provider.user.mobile} - {self.date} {self.start_time}-{self.end_time}"
