from django.utils import timezone
from django.db import transaction, models
from datetime import datetime, timedelta, time
from .models import (
    SlotConfiguration, WorkingShift, HolidaySchedule,
    TimeSlot, SlotBlockage
)


class SlotGenerationService:
    """Service for generating time slots based on configuration."""
    
    @staticmethod
    def generate_slots(configuration, start_date, end_date):
        """
        Generate time slots for a date range based on configuration.
        
        Args:
            configuration: SlotConfiguration instance
            start_date: Start date for generation
            end_date: End date for generation
            
        Returns:
            int: Number of slots generated
        """
        generated_count = 0
        current_date = start_date
        
        # Get working shifts for this configuration
        working_shifts = WorkingShift.objects.filter(
            configuration=configuration
        ).order_by('weekday')
        
        # Create a mapping of weekday to shifts
        shifts_by_weekday = {}
        for shift in working_shifts:
            shifts_by_weekday[shift.weekday] = shift
        
        # Get holidays in the date range
        holidays = set(
            HolidaySchedule.objects.filter(
                date__range=[start_date, end_date],
                is_active=True
            ).values_list('date', flat=True)
        )
        
        # Get active blockages
        blockages = SlotBlockage.objects.filter(
            is_active=True,
            start_date__lte=end_date,
            end_date__gte=start_date
        )
        
        with transaction.atomic():
            while current_date <= end_date:
                # Skip holidays
                if current_date in holidays:
                    current_date += timedelta(days=1)
                    continue
                
                # Get working shift for this weekday
                weekday = current_date.weekday()
                shift = shifts_by_weekday.get(weekday)
                
                if not shift or not shift.is_working_day:
                    current_date += timedelta(days=1)
                    continue
                
                # Generate slots for this day
                day_slots = SlotGenerationService._generate_slots_for_day(
                    configuration=configuration,
                    date=current_date,
                    shift=shift,
                    blockages=blockages
                )
                
                generated_count += day_slots
                current_date += timedelta(days=1)
        
        return generated_count
    
    @staticmethod
    def _generate_slots_for_day(configuration, date, shift, blockages):
        """Generate slots for a specific day."""
        generated_count = 0
        
        # Calculate actual start time with buffer
        buffer_minutes = configuration.buffer_time_minutes
        actual_start_time = SlotGenerationService._add_minutes_to_time(
            shift.start_time, buffer_minutes
        )
        
        # Generate slots from actual start time to end time
        current_time = actual_start_time
        slot_duration = timedelta(minutes=configuration.slot_interval_minutes)
        
        while current_time < shift.end_time:
            slot_end_time = SlotGenerationService._add_minutes_to_time(
                current_time, configuration.slot_interval_minutes
            )
            
            # Don't create slot if it would extend beyond shift end time
            if slot_end_time > shift.end_time:
                break
            
            # Check if this slot is blocked
            is_blocked = SlotGenerationService._is_slot_blocked(
                date, current_time, slot_end_time, blockages
            )
            
            # Create or update time slot
            slot, created = TimeSlot.objects.get_or_create(
                configuration=configuration,
                date=date,
                start_time=current_time,
                defaults={
                    'end_time': slot_end_time,
                    'status': 'blocked' if is_blocked else 'available',
                    'max_bookings': shift.max_bookings_per_slot,
                    'blocked_reason': 'Blocked by schedule' if is_blocked else '',
                }
            )
            
            if created:
                generated_count += 1
            
            # Move to next slot
            current_time = slot_end_time
        
        return generated_count
    
    @staticmethod
    def _add_minutes_to_time(time_obj, minutes):
        """Add minutes to a time object."""
        dt = datetime.combine(datetime.today(), time_obj)
        dt += timedelta(minutes=minutes)
        return dt.time()
    
    @staticmethod
    def _is_slot_blocked(date, start_time, end_time, blockages):
        """Check if a slot is blocked by any blockage rules."""
        for blockage in blockages:
            if SlotGenerationService._blockage_affects_slot(
                blockage, date, start_time, end_time
            ):
                return True
        return False
    
    @staticmethod
    def _blockage_affects_slot(blockage, date, start_time, end_time):
        """Check if a blockage affects a specific slot."""
        # Check date range
        if blockage.start_date > date:
            return False
        if blockage.end_date and blockage.end_date < date:
            return False
        
        # Check blockage type
        if blockage.blockage_type == 'full_day':
            return True
        
        if blockage.blockage_type in ['single_slot', 'time_range']:
            # Check time overlap
            if not blockage.start_time or not blockage.end_time:
                return False
            
            # Check if times overlap
            return not (
                end_time <= blockage.start_time or 
                start_time >= blockage.end_time
            )
        
        # Handle recurring blockages
        if blockage.is_recurring and blockage.recurrence_pattern:
            return SlotGenerationService._check_recurring_blockage(
                blockage, date, start_time, end_time
            )
        
        return False
    
    @staticmethod
    def _check_recurring_blockage(blockage, date, start_time, end_time):
        """Check if a recurring blockage affects a slot."""
        if blockage.recurrence_pattern == 'weekly':
            # Check if it's the same weekday
            if date.weekday() == blockage.start_date.weekday():
                if blockage.blockage_type == 'full_day':
                    return True
                elif blockage.start_time and blockage.end_time:
                    return not (
                        end_time <= blockage.start_time or 
                        start_time >= blockage.end_time
                    )
        
        # Add more recurrence patterns as needed
        return False


class SlotAvailabilityService:
    """Service for checking slot availability."""
    
    @staticmethod
    def get_available_slots(date, service_duration_minutes=None):
        """
        Get available slots for a specific date.
        
        Args:
            date: Date to check
            service_duration_minutes: Optional service duration filter
            
        Returns:
            QuerySet: Available TimeSlot objects
        """
        # Base query for available slots
        slots = TimeSlot.objects.filter(
            date=date,
            status='available',
            current_bookings__lt=models.F('max_bookings')
        ).order_by('start_time')
        
        # Filter by service duration if provided
        if service_duration_minutes:
            slots = SlotAvailabilityService._filter_by_service_duration(
                slots, service_duration_minutes
            )
        
        return slots
    
    @staticmethod
    def _filter_by_service_duration(slots, service_duration_minutes):
        """Filter slots that can accommodate the service duration."""
        # This is a simplified implementation
        # In a more complex system, you might need to check consecutive slots
        # or slots that have enough time for the service
        
        filtered_slots = []
        for slot in slots:
            slot_duration = SlotAvailabilityService._calculate_slot_duration(slot)
            if slot_duration >= service_duration_minutes:
                filtered_slots.append(slot)
        
        return filtered_slots
    
    @staticmethod
    def _calculate_slot_duration(slot):
        """Calculate slot duration in minutes."""
        start_dt = datetime.combine(slot.date, slot.start_time)
        end_dt = datetime.combine(slot.date, slot.end_time)
        duration = end_dt - start_dt
        return int(duration.total_seconds() / 60)
    
    @staticmethod
    def check_slot_availability(slot_id, quantity=1):
        """
        Check if a specific slot can accommodate the requested quantity.
        
        Args:
            slot_id: TimeSlot ID
            quantity: Number of bookings requested
            
        Returns:
            tuple: (is_available, message)
        """
        try:
            slot = TimeSlot.objects.get(id=slot_id)
            
            if slot.status != 'available':
                return False, f"Slot is {slot.status}"
            
            if slot.date < timezone.now().date():
                return False, "Cannot book slots in the past"
            
            if slot.current_bookings + quantity > slot.max_bookings:
                remaining = slot.max_bookings - slot.current_bookings
                return False, f"Only {remaining} slots available"
            
            return True, "Slot is available"
        
        except TimeSlot.DoesNotExist:
            return False, "Slot not found"
    
    @staticmethod
    def get_next_available_slots(date, count=10):
        """
        Get next available slots starting from a date.
        
        Args:
            date: Starting date
            count: Number of slots to return
            
        Returns:
            list: Available TimeSlot objects
        """
        slots = TimeSlot.objects.filter(
            date__gte=date,
            status='available',
            current_bookings__lt=models.F('max_bookings')
        ).order_by('date', 'start_time')[:count]
        
        return list(slots)


class SlotMaintenanceService:
    """Service for slot maintenance operations."""
    
    @staticmethod
    def cleanup_past_slots(days_to_keep=30):
        """
        Clean up old slots to maintain database performance.
        
        Args:
            days_to_keep: Number of days of past slots to keep
            
        Returns:
            int: Number of slots deleted
        """
        cutoff_date = timezone.now().date() - timedelta(days=days_to_keep)
        
        deleted_count, _ = TimeSlot.objects.filter(
            date__lt=cutoff_date,
            status__in=['available', 'blocked']  # Don't delete booked slots
        ).delete()
        
        return deleted_count
    
    @staticmethod
    def update_slot_statuses():
        """Update slot statuses based on current bookings."""
        # Update slots that should be marked as booked
        TimeSlot.objects.filter(
            current_bookings__gte=models.F('max_bookings'),
            status='available'
        ).update(status='booked')
        
        # Update slots that should be marked as available
        TimeSlot.objects.filter(
            current_bookings__lt=models.F('max_bookings'),
            status='booked'
        ).update(status='available')
    
    @staticmethod
    def generate_slots_for_next_days(configuration, days=7):
        """
        Generate slots for the next N days.
        
        Args:
            configuration: SlotConfiguration instance
            days: Number of days to generate slots for
            
        Returns:
            int: Number of slots generated
        """
        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=days)
        
        return SlotGenerationService.generate_slots(
            configuration=configuration,
            start_date=start_date,
            end_date=end_date
        )
