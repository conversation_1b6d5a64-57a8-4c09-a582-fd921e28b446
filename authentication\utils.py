import random
import requests
import logging
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta

logger = logging.getLogger(__name__)


class OTPService:
    """
    Service for handling OTP generation, sending, and verification
    """
    
    @staticmethod
    def generate_otp(length=6):
        """
        Generate a random OTP of specified length
        """
        return ''.join([str(random.randint(0, 9)) for _ in range(length)])
    
    @staticmethod
    def send_otp(mobile_number, otp=None):
        """
        Send OTP to mobile number using MSG91
        """
        if not otp:
            otp = OTPService.generate_otp()

        # Store OTP in cache for 10 minutes
        cache_key = f"otp_{mobile_number}"
        cache.set(cache_key, otp, timeout=600)  # 10 minutes

        # Send OTP via MSG91
        success = OTPService._send_via_msg91(mobile_number, otp)

        if success:
            logger.info(f"OTP sent successfully to {mobile_number}")
            return True, otp
        else:
            logger.error(f"Failed to send OTP to {mobile_number}")
            return False, None
    
    @staticmethod
    def verify_otp(mobile_number, otp):
        """
        Verify OTP using MSG91 API
        """
        # First check cache for development/fallback
        cache_key = f"otp_{mobile_number}"
        stored_otp = cache.get(cache_key)

        # Use MSG91 verify API
        success = OTPService._verify_via_msg91(mobile_number, otp)

        if success:
            # Remove OTP from cache after successful verification
            cache.delete(cache_key)
            logger.info(f"OTP verified successfully for {mobile_number}")
            return True

        logger.warning(f"OTP verification failed for {mobile_number}")
        return False

    @staticmethod
    def _verify_via_msg91(mobile_number, otp):
        """
        Verify OTP using MSG91 API v5
        """
        try:
            # Remove country code if present for MSG91
            clean_mobile = mobile_number.replace('+91', '').replace('+', '')

            # MSG91 OTP verify API v5 endpoint
            url = "https://control.msg91.com/api/v5/otp/verify"

            # Query parameters as per MSG91 documentation
            params = {
                'otp': otp,
                'mobile': f"91{clean_mobile}"  # Include country code
            }

            headers = {
                'authkey': settings.MSG91_AUTH_KEY
            }

            logger.info(f"Verifying OTP for {clean_mobile} via MSG91")
            response = requests.get(url, params=params, headers=headers, timeout=10)

            logger.info(f"MSG91 Verify Response: {response.status_code} - {response.text}")

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if response_data.get('type') == 'success':
                        return True
                    else:
                        logger.error(f"MSG91 verify error: {response_data}")
                        return False
                except ValueError:
                    # Sometimes MSG91 returns plain text success response
                    if 'success' in response.text.lower() or 'verified' in response.text.lower():
                        return True
                    else:
                        logger.error(f"MSG91 verify unexpected response: {response.text}")
                        return False
            else:
                logger.error(f"MSG91 verify API error: {response.status_code} - {response.text}")
                return False

        except requests.RequestException as e:
            logger.error(f"MSG91 verify request failed: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error verifying OTP: {str(e)}")
            return False
    
    @staticmethod
    def _send_via_msg91(mobile_number, otp):
        """
        Send OTP using MSG91 API v5 (matching official documentation)
        """
        try:
            # Remove country code if present for MSG91
            clean_mobile = mobile_number.replace('+91', '').replace('+', '')

            # MSG91 OTP API v5 endpoint with query parameters (as per documentation)
            url = "https://control.msg91.com/api/v5/otp"

            # Query parameters as per MSG91 documentation
            params = {
                'template_id': settings.MSG91_TEMPLATE_ID,
                'mobile': f"91{clean_mobile}",  # Include country code
                'authkey': settings.MSG91_AUTH_KEY,
                'otp_expiry': 10,  # 10 minutes
                'realTimeResponse': 1  # Get real-time response
            }

            # Body data for template variables (if any)
            payload = {
                "otp": otp  # Pass OTP as template variable
            }

            headers = {
                'Content-Type': 'application/JSON',
                'content-type': 'application/json'
            }

            logger.info(f"Sending OTP to {clean_mobile} via MSG91")
            response = requests.post(url, params=params, json=payload, headers=headers, timeout=10)

            logger.info(f"MSG91 Response: {response.status_code} - {response.text}")

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if response_data.get('type') == 'success':
                        return True
                    else:
                        logger.error(f"MSG91 API error: {response_data}")
                        return False
                except ValueError:
                    # Sometimes MSG91 returns plain text success response
                    if 'success' in response.text.lower():
                        return True
                    else:
                        logger.error(f"MSG91 unexpected response: {response.text}")
                        return False
            else:
                logger.error(f"MSG91 API error: {response.status_code} - {response.text}")
                return False

        except requests.RequestException as e:
            logger.error(f"MSG91 request failed: {str(e)}")
            return False

    @staticmethod
    def resend_otp(mobile_number, retry_type='voice'):
        """
        Resend OTP using MSG91 retry API
        """
        try:
            # Remove country code if present for MSG91
            clean_mobile = mobile_number.replace('+91', '').replace('+', '')

            # MSG91 OTP retry API v5 endpoint
            url = "https://control.msg91.com/api/v5/otp/retry"

            # Query parameters as per MSG91 documentation
            params = {
                'authkey': settings.MSG91_AUTH_KEY,
                'retrytype': retry_type,  # 'voice' or 'text'
                'mobile': f"91{clean_mobile}"  # Include country code
            }

            logger.info(f"Resending OTP to {clean_mobile} via MSG91 ({retry_type})")
            response = requests.get(url, params=params, timeout=10)

            logger.info(f"MSG91 Resend Response: {response.status_code} - {response.text}")

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if response_data.get('type') == 'success':
                        return True
                    else:
                        logger.error(f"MSG91 resend error: {response_data}")
                        return False
                except ValueError:
                    # Sometimes MSG91 returns plain text success response
                    if 'success' in response.text.lower():
                        return True
                    else:
                        logger.error(f"MSG91 resend unexpected response: {response.text}")
                        return False
            else:
                logger.error(f"MSG91 resend API error: {response.status_code} - {response.text}")
                return False

        except requests.RequestException as e:
            logger.error(f"MSG91 resend request failed: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error resending OTP: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending OTP: {str(e)}")
            return False


class RateLimitService:
    """
    Service for handling rate limiting
    """
    
    @staticmethod
    def is_rate_limited(key, limit, window_seconds):
        """
        Check if a key is rate limited
        """
        cache_key = f"rate_limit_{key}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= limit:
            return True
        
        # Increment counter
        cache.set(cache_key, current_count + 1, timeout=window_seconds)
        return False
    
    @staticmethod
    def reset_rate_limit(key):
        """
        Reset rate limit for a key
        """
        cache_key = f"rate_limit_{key}"
        cache.delete(cache_key)
    
    @staticmethod
    def get_rate_limit_info(key):
        """
        Get current rate limit info for a key
        """
        cache_key = f"rate_limit_{key}"
        current_count = cache.get(cache_key, 0)
        ttl = cache.ttl(cache_key)

        return {
            'current_count': current_count,
            'ttl': ttl
        }

    @staticmethod
    def clear_user_rate_limits(user):
        """
        Clear all rate limits for a specific user
        """
        cleared_keys = []

        # Clear OTP rate limits
        if user.mobile_number:
            otp_key = f"otp_send_{user.mobile_number}"
            RateLimitService.reset_rate_limit(otp_key)
            cleared_keys.append(otp_key)

            # Also clear mobile login attempts
            mobile_login_key = f"login_attempt_{user.mobile_number}"
            RateLimitService.reset_rate_limit(mobile_login_key)
            cleared_keys.append(mobile_login_key)

        # Clear email login rate limits
        if user.email:
            email_login_key = f"login_attempt_{user.email}"
            RateLimitService.reset_rate_limit(email_login_key)
            cleared_keys.append(email_login_key)

        # Clear user-specific rate limits
        user_key = f"user_action_{user.id}"
        RateLimitService.reset_rate_limit(user_key)
        cleared_keys.append(user_key)

        return cleared_keys


class SecurityService:
    """
    Service for security-related operations
    """
    
    @staticmethod
    def get_client_ip(request):
        """
        Get client IP address from request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip
    
    @staticmethod
    def is_ip_blocked(ip_address):
        """
        Check if IP address is blocked
        """
        cache_key = f"blocked_ip_{ip_address}"
        return cache.get(cache_key, False)
    
    @staticmethod
    def block_ip(ip_address, duration_minutes=60):
        """
        Block IP address for specified duration
        """
        cache_key = f"blocked_ip_{ip_address}"
        cache.set(cache_key, True, timeout=duration_minutes * 60)
        logger.warning(f"IP address blocked: {ip_address}")
    
    @staticmethod
    def unblock_ip(ip_address):
        """
        Unblock IP address
        """
        cache_key = f"blocked_ip_{ip_address}"
        cache.delete(cache_key)
        logger.info(f"IP address unblocked: {ip_address}")


def validate_mobile_number(mobile_number):
    """
    Validate mobile number format
    """
    import re
    
    # Remove spaces and special characters
    clean_mobile = re.sub(r'[^\d+]', '', mobile_number)
    
    # Check if it matches Indian mobile number pattern
    pattern = r'^(\+91|91)?[6-9]\d{9}$'
    
    if re.match(pattern, clean_mobile):
        # Normalize to +91 format
        if clean_mobile.startswith('+91'):
            return clean_mobile
        elif clean_mobile.startswith('91'):
            return '+' + clean_mobile
        else:
            return '+91' + clean_mobile
    
    return None


def generate_username_from_mobile(mobile_number):
    """
    Generate username from mobile number
    """
    clean_mobile = mobile_number.replace('+', '').replace('-', '').replace(' ', '')
    return f"user_{clean_mobile}"


def mask_mobile_number(mobile_number):
    """
    Mask mobile number for display (e.g., +91XXXXXX1234)
    """
    if len(mobile_number) >= 4:
        return mobile_number[:-4].replace(mobile_number[3:-4], 'X' * len(mobile_number[3:-4])) + mobile_number[-4:]
    return mobile_number


def mask_email(email):
    """
    Mask email for display (e.g., u***@example.com)
    """
    if '@' in email:
        local, domain = email.split('@', 1)
        if len(local) > 2:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        else:
            masked_local = local[0] + '*'
        return f"{masked_local}@{domain}"
    return email
