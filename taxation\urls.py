from django.urls import path
from . import views

app_name = 'taxation'

urlpatterns = [
    path('quick-setup/', views.tax_quick_setup, name='quick_setup'),
    path('calculate-preview/', views.calculate_tax_preview, name='calculate_preview'),

    # Admin API endpoints for staff dashboard
    path('admin/tax-categories/', views.AdminTaxCategoryListView.as_view(), name='admin-tax-category-list'),
    path('admin/gst-rates/', views.AdminGSTRateListView.as_view(), name='admin-gst-rate-list'),
    path('admin/tax-configurations/', views.AdminTaxConfigurationListView.as_view(), name='admin-tax-config-list'),
]
