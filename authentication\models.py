from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.db import models
from django.utils import timezone
from django.core.validators import RegexValidator
from .managers import UserManager


class User(AbstractBaseUser, PermissionsMixin):
    """
    Custom User model supporting email and mobile authentication
    """
    USER_TYPE_CHOICES = [
        ('CUSTOMER', 'Customer'),
        ('PROVIDER', 'Provider'),
        ('STAFF', 'Staff'),
    ]

    email = models.EmailField(
        unique=True,
        null=True,
        blank=True,
        help_text="Required for staff users"
    )

    mobile_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Mobile number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    mobile_number = models.CharField(
        validators=[mobile_regex],
        max_length=17,
        unique=True,
        null=True,
        blank=True,
        help_text="Required for customer and provider users"
    )

    name = models.CharField(max_length=150)
    user_type = models.CharField(
        max_length=10,
        choices=USER_TYPE_CHOICES,
        default='CUSTOMER'
    )

    is_verified = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_locked = models.BooleanField(default=False)
    lockout_until = models.DateTimeField(null=True, blank=True)

    profile_picture = models.ImageField(
        upload_to='profile_pictures/',
        null=True,
        blank=True
    )

    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(null=True, blank=True)

    objects = UserManager()

    USERNAME_FIELD = 'email'  # Default, but can be overridden by backends
    REQUIRED_FIELDS = ['name']

    class Meta:
        db_table = 'auth_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        if self.email:
            return f"{self.name} ({self.email})"
        return f"{self.name} ({self.mobile_number})"

    def clean(self):
        from django.core.exceptions import ValidationError

        # Staff users must have email
        if self.user_type == 'STAFF' and not self.email:
            raise ValidationError("Staff users must have an email address")

        # Customer and Provider users must have mobile number
        if self.user_type in ['CUSTOMER', 'PROVIDER'] and not self.mobile_number:
            raise ValidationError("Customer and Provider users must have a mobile number")

    def is_account_locked(self):
        """Check if account is currently locked"""
        if not self.is_locked:
            return False
        if self.lockout_until and timezone.now() > self.lockout_until:
            # Unlock account if lockout period has expired
            self.is_locked = False
            self.lockout_until = None
            self.save(update_fields=['is_locked', 'lockout_until'])
            return False
        return True

    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        self.is_locked = True
        self.lockout_until = timezone.now() + timezone.timedelta(minutes=duration_minutes)
        self.save(update_fields=['is_locked', 'lockout_until'])

    def unlock_account(self):
        """Manually unlock account"""
        self.is_locked = False
        self.lockout_until = None
        self.save(update_fields=['is_locked', 'lockout_until'])


class Address(models.Model):
    """
    User address model supporting multiple addresses per user
    """
    ADDRESS_TYPE_CHOICES = [
        ('HOME', 'Home'),
        ('WORK', 'Work'),
        ('OTHER', 'Other'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='addresses'
    )
    address_type = models.CharField(
        max_length=10,
        choices=ADDRESS_TYPE_CHOICES,
        default='HOME'
    )
    street = models.TextField()
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    zip_code = models.CharField(max_length=10)
    landmark = models.CharField(max_length=200, blank=True)
    is_default = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_addresses'
        verbose_name = 'Address'
        verbose_name_plural = 'Addresses'
        ordering = ['-is_default', '-created_at']

    def __str__(self):
        return f"{self.user.name} - {self.address_type} ({self.city})"

    def save(self, *args, **kwargs):
        # Ensure only one default address per user
        if self.is_default:
            Address.objects.filter(
                user=self.user,
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)

        # If this is the user's first address, make it default
        if not Address.objects.filter(user=self.user).exists():
            self.is_default = True

        super().save(*args, **kwargs)


class FailedLoginAttempt(models.Model):
    """
    Track failed login attempts for security purposes
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='failed_attempts'
    )
    mobile_number = models.CharField(max_length=17, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'failed_login_attempts'
        verbose_name = 'Failed Login Attempt'
        verbose_name_plural = 'Failed Login Attempts'
        ordering = ['-timestamp']

    def __str__(self):
        identifier = self.email or self.mobile_number or 'Unknown'
        return f"Failed attempt: {identifier} from {self.ip_address} at {self.timestamp}"
