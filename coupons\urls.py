from django.urls import path
from . import views

app_name = 'coupons'

urlpatterns = [
    # Coupon CRUD (Admin)
    path('', views.CouponListCreateView.as_view(), name='coupon-list-create'),
    path('<str:code>/', views.CouponDetailView.as_view(), name='coupon-detail'),
    
    # Coupon validation and application
    path('validate/', views.validate_coupon, name='validate-coupon'),
    path('apply/', views.apply_coupon, name='apply-coupon'),
    path('apply-sequential/', views.apply_sequential_coupons, name='apply-sequential-coupons'),
    path('redeem/', views.redeem_coupon, name='redeem-coupon'),
    
    # Usage tracking
    path('usage/', views.UsedCouponListView.as_view(), name='coupon-usage'),

    # Admin endpoints for staff dashboard
    path('admin/coupons/', views.AdminCouponListView.as_view(), name='admin-coupon-list'),
    path('admin/used-coupons/', views.AdminUsedCouponListView.as_view(), name='admin-used-coupon-list'),
]
