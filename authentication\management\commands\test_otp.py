from django.core.management.base import BaseCommand
from authentication.utils import OTPService


class Command(BaseCommand):
    help = 'Test OTP service functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--mobile',
            type=str,
            help='Mobile number to test OTP (e.g., +919876543210)',
            required=True
        )
        parser.add_argument(
            '--send',
            action='store_true',
            help='Send OTP to the mobile number',
        )
        parser.add_argument(
            '--verify',
            type=str,
            help='Verify OTP code',
        )

    def handle(self, *args, **options):
        mobile_number = options['mobile']
        
        if options['send']:
            self.stdout.write(f"Sending OTP to {mobile_number}...")
            success, otp = OTPService.send_otp(mobile_number)
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ OTP sent successfully to {mobile_number}')
                )
                # In development, show the OTP for testing
                if otp:
                    self.stdout.write(f"🔐 OTP for testing: {otp}")
            else:
                self.stdout.write(
                    self.style.ERROR(f'❌ Failed to send OTP to {mobile_number}')
                )
        
        elif options['verify']:
            otp_code = options['verify']
            self.stdout.write(f"Verifying OTP {otp_code} for {mobile_number}...")
            
            if OTPService.verify_otp(mobile_number, otp_code):
                self.stdout.write(
                    self.style.SUCCESS(f'✅ OTP verified successfully for {mobile_number}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'❌ Invalid or expired OTP for {mobile_number}')
                )
        
        else:
            self.stdout.write(
                self.style.WARNING('Please specify either --send or --verify option')
            )
            self.stdout.write('Examples:')
            self.stdout.write('  python manage.py test_otp --mobile +919876543210 --send')
            self.stdout.write('  python manage.py test_otp --mobile +919876543210 --verify 123456')
