{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Quick Tax Setup - {{ site_title|default:"Django site admin" }}{% endblock %}

{% block extrahead %}
<style>
.tax-setup-form {
    max-width: 800px;
    margin: 20px auto;
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: center;
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus {
    border-color: #007cba;
    outline: none;
    box-shadow: 0 0 5px rgba(0,124,186,0.3);
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #007cba;
    color: white;
}

.btn-primary:hover {
    background: #005a87;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    margin-left: 10px;
}

.current-config {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 30px;
    border-left: 4px solid #007cba;
}

.preview-section {
    background: #e8f4f8;
    padding: 20px;
    border-radius: 4px;
    margin-top: 20px;
}

.calculation-result {
    display: none;
    margin-top: 15px;
    padding: 15px;
    background: white;
    border-radius: 4px;
    border: 1px solid #ddd;
}
</style>
{% endblock %}

{% block content %}
<div class="tax-setup-form">
    <h1>🏷️ Quick Tax Setup</h1>
    <p>Configure GST rates and service charges for your platform.</p>

    {% if current_config %}
    <div class="current-config">
        <h3>📊 Current Configuration</h3>
        <p><strong>Name:</strong> {{ current_config.name }}</p>
        <p><strong>Service Charge:</strong> {{ current_config.service_charge_percentage }}%</p>
        {% if current_rates %}
        <p><strong>GST Rates:</strong>
            {% if current_rates.CGST %}CGST: {{ current_rates.CGST }}%{% endif %}
            {% if current_rates.SGST %}, SGST: {{ current_rates.SGST }}%{% endif %}
            {% if current_rates.IGST %}, IGST: {{ current_rates.IGST }}%{% endif %}
        </p>
        {% endif %}
    </div>
    {% endif %}

    <form method="post">
        {% csrf_token %}
        
        <div class="form-row">
            <div class="form-group">
                <label for="category_name">Tax Category Name:</label>
                <input type="text" id="category_name" name="category_name" 
                       value="{% if current_config.default_tax_category %}{{ current_config.default_tax_category.name }}{% else %}Home Services{% endif %}" required>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="cgst_rate">CGST Rate (%):</label>
                <input type="number" id="cgst_rate" name="cgst_rate" step="0.01" min="0" max="50"
                       value="{% if current_rates.CGST %}{{ current_rates.CGST }}{% else %}9.00{% endif %}" required>
            </div>
            <div class="form-group">
                <label for="sgst_rate">SGST Rate (%):</label>
                <input type="number" id="sgst_rate" name="sgst_rate" step="0.01" min="0" max="50"
                       value="{% if current_rates.SGST %}{{ current_rates.SGST }}{% else %}9.00{% endif %}" required>
            </div>
            <div class="form-group">
                <label for="igst_rate">IGST Rate (%):</label>
                <input type="number" id="igst_rate" name="igst_rate" step="0.01" min="0" max="50"
                       value="{% if current_rates.IGST %}{{ current_rates.IGST }}{% else %}18.00{% endif %}" required>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="service_charge">Platform Service Charge (%):</label>
                <input type="number" id="service_charge" name="service_charge" step="0.01" min="0" max="20"
                       value="{% if current_config %}{{ current_config.service_charge_percentage }}{% else %}2.50{% endif %}" required>
            </div>
        </div>

        <div class="form-row">
            <button type="submit" class="btn btn-primary">💾 Update Tax Configuration</button>
            <a href="{% url 'admin:taxation_taxconfiguration_changelist' %}" class="btn btn-secondary">📋 View All Configurations</a>
        </div>
    </form>

    <div class="preview-section">
        <h3>🧮 Tax Calculator Preview</h3>
        <p>Enter an amount to see how taxes will be calculated:</p>
        
        <div class="form-row">
            <div class="form-group">
                <label for="preview_amount">Service Amount (₹):</label>
                <input type="number" id="preview_amount" step="0.01" min="0" placeholder="1000.00">
            </div>
            <button type="button" id="calculate_preview" class="btn btn-primary">Calculate</button>
        </div>

        <div id="calculation_result" class="calculation-result">
            <!-- Results will be populated by JavaScript -->
        </div>
    </div>
</div>

<script>
document.getElementById('calculate_preview').addEventListener('click', function() {
    const amount = document.getElementById('preview_amount').value;
    if (!amount || amount <= 0) {
        alert('Please enter a valid amount');
        return;
    }

    fetch('{% url "admin:taxation_calculate_preview" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: 'amount=' + encodeURIComponent(amount)
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('calculation_result');
        if (data.success) {
            const calc = data.calculation;
            resultDiv.innerHTML = `
                <h4>💰 Tax Calculation for ₹${calc.subtotal}</h4>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td style="padding: 5px; border-bottom: 1px solid #eee;"><strong>Subtotal:</strong></td><td style="padding: 5px; border-bottom: 1px solid #eee;">₹${calc.subtotal}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #eee;">CGST:</td><td style="padding: 5px; border-bottom: 1px solid #eee;">₹${calc.cgst}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #eee;">SGST:</td><td style="padding: 5px; border-bottom: 1px solid #eee;">₹${calc.sgst}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #eee;">Service Charge:</td><td style="padding: 5px; border-bottom: 1px solid #eee;">₹${calc.service_charge}</td></tr>
                    <tr style="background: #f8f9fa;"><td style="padding: 8px; font-weight: bold;">Total Tax:</td><td style="padding: 8px; font-weight: bold;">₹${calc.total_tax}</td></tr>
                    <tr style="background: #e8f4f8;"><td style="padding: 8px; font-weight: bold; font-size: 16px;">Grand Total:</td><td style="padding: 8px; font-weight: bold; font-size: 16px;">₹${calc.grand_total}</td></tr>
                </table>
            `;
            resultDiv.style.display = 'block';
        } else {
            resultDiv.innerHTML = `<div style="color: red;">Error: ${data.error}</div>`;
            resultDiv.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error calculating tax preview');
    });
});
</script>
{% endblock %}
