import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _


def validate_mobile_number(value):
    """
    Validate Indian mobile number format
    """
    if not value:
        return
    
    # Remove spaces and special characters except +
    clean_value = re.sub(r'[^\d+]', '', value)
    
    # Check if it matches Indian mobile number pattern
    pattern = r'^(\+91|91)?[6-9]\d{9}$'
    
    if not re.match(pattern, clean_value):
        raise ValidationError(
            _('Enter a valid Indian mobile number. Format: +91XXXXXXXXXX or XXXXXXXXXX'),
            code='invalid_mobile'
        )


def validate_password_strength(password):
    """
    Validate password strength
    """
    if len(password) < 8:
        raise ValidationError(
            _('Password must be at least 8 characters long.'),
            code='password_too_short'
        )
    
    if not re.search(r'[A-Z]', password):
        raise ValidationError(
            _('Password must contain at least one uppercase letter.'),
            code='password_no_upper'
        )
    
    if not re.search(r'[a-z]', password):
        raise ValidationError(
            _('Password must contain at least one lowercase letter.'),
            code='password_no_lower'
        )
    
    if not re.search(r'\d', password):
        raise ValidationError(
            _('Password must contain at least one digit.'),
            code='password_no_digit'
        )
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        raise ValidationError(
            _('Password must contain at least one special character.'),
            code='password_no_special'
        )


def validate_otp(value):
    """
    Validate OTP format
    """
    if not value:
        raise ValidationError(
            _('OTP is required.'),
            code='otp_required'
        )
    
    if not value.isdigit():
        raise ValidationError(
            _('OTP must contain only digits.'),
            code='otp_invalid_format'
        )
    
    if len(value) != 6:
        raise ValidationError(
            _('OTP must be exactly 6 digits.'),
            code='otp_invalid_length'
        )


def validate_name(value):
    """
    Validate user name
    """
    if not value or not value.strip():
        raise ValidationError(
            _('Name is required.'),
            code='name_required'
        )
    
    if len(value.strip()) < 2:
        raise ValidationError(
            _('Name must be at least 2 characters long.'),
            code='name_too_short'
        )
    
    if len(value.strip()) > 150:
        raise ValidationError(
            _('Name must be less than 150 characters.'),
            code='name_too_long'
        )
    
    # Check for valid characters (letters, spaces, hyphens, apostrophes)
    if not re.match(r"^[a-zA-Z\s\-'\.]+$", value.strip()):
        raise ValidationError(
            _('Name can only contain letters, spaces, hyphens, and apostrophes.'),
            code='name_invalid_characters'
        )


def validate_user_type(value):
    """
    Validate user type
    """
    valid_types = ['CUSTOMER', 'PROVIDER', 'STAFF']
    if value not in valid_types:
        raise ValidationError(
            _('Invalid user type. Must be one of: %(valid_types)s') % {
                'valid_types': ', '.join(valid_types)
            },
            code='invalid_user_type'
        )


def validate_address_type(value):
    """
    Validate address type
    """
    valid_types = ['HOME', 'WORK', 'OTHER']
    if value not in valid_types:
        raise ValidationError(
            _('Invalid address type. Must be one of: %(valid_types)s') % {
                'valid_types': ', '.join(valid_types)
            },
            code='invalid_address_type'
        )


def validate_zip_code(value):
    """
    Validate Indian ZIP code (PIN code)
    """
    if not value:
        return
    
    # Indian PIN code pattern: 6 digits
    if not re.match(r'^\d{6}$', value):
        raise ValidationError(
            _('Enter a valid PIN code (6 digits).'),
            code='invalid_zip_code'
        )


def validate_image_file(value):
    """
    Validate uploaded image file
    """
    if not value:
        return
    
    # Check file size (max 5MB)
    if value.size > 5 * 1024 * 1024:
        raise ValidationError(
            _('Image file size must be less than 5MB.'),
            code='file_too_large'
        )
    
    # Check file extension
    valid_extensions = ['.jpg', '.jpeg', '.png', '.gif']
    file_extension = value.name.lower().split('.')[-1]
    
    if f'.{file_extension}' not in valid_extensions:
        raise ValidationError(
            _('Invalid file format. Allowed formats: %(formats)s') % {
                'formats': ', '.join(valid_extensions)
            },
            code='invalid_file_format'
        )


class PasswordValidator:
    """
    Custom password validator class
    """
    
    def validate(self, password, user=None):
        validate_password_strength(password)
    
    def get_help_text(self):
        return _(
            "Your password must contain at least 8 characters, including "
            "uppercase and lowercase letters, digits, and special characters."
        )
