# Generated manually to handle existing service_id column

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cart', '0002_alter_cart_user'),
    ]

    operations = [
        # Add service_title field
        migrations.AddField(
            model_name='cartitem',
            name='service_title',
            field=models.CharField(default='Unknown Service', max_length=255),
            preserve_default=False,
        ),
        # Add service_image_url field
        migrations.AddField(
            model_name='cartitem',
            name='service_image_url',
            field=models.URLField(blank=True, null=True),
        ),
        # Update unique constraint to use service_id instead of service
        migrations.AlterUniqueTogether(
            name='cartitem',
            unique_together={('cart', 'service_id')},
        ),
    ]
