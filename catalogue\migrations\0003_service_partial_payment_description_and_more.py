# Generated by Django 4.2.7 on 2025-06-17 08:22

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0002_add_seo_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='service',
            name='partial_payment_description',
            field=models.TextField(blank=True, help_text='Description for partial payment requirement', null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='partial_payment_type',
            field=models.CharField(choices=[('percentage', 'Percentage'), ('fixed', 'Fixed Amount')], default='percentage', help_text='Type of partial payment calculation', max_length=10),
        ),
        migrations.AddField(
            model_name='service',
            name='partial_payment_value',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Partial payment amount (percentage or fixed amount)', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))]),
        ),
        migrations.AddField(
            model_name='service',
            name='requires_partial_payment',
            field=models.BooleanField(default=False, help_text='Whether this service requires partial payment in advance'),
        ),
    ]
