from django.test import TestCase
from django.utils import timezone
from datetime import date, time, timedelta
from .models import (
    SlotConfiguration, WorkingShift, HolidaySchedule,
    TimeSlot, SlotBlockage, SlotBooking
)
from .services import SlotGenerationService, SlotAvailabilityService


class SlotConfigurationTestCase(TestCase):
    """Test cases for SlotConfiguration model."""
    
    def setUp(self):
        self.config = SlotConfiguration.objects.create(
            name="Test Configuration",
            slot_interval_minutes=60,
            buffer_time_minutes=15,
            advance_booking_days=30,
            same_day_booking_cutoff_hours=2
        )
    
    def test_slot_configuration_creation(self):
        """Test slot configuration creation."""
        self.assertEqual(self.config.name, "Test Configuration")
        self.assertEqual(self.config.slot_interval_minutes, 60)
        self.assertEqual(self.config.buffer_time_minutes, 15)
        self.assertTrue(self.config.is_active)
    
    def test_slot_configuration_str(self):
        """Test string representation."""
        expected = "Test Configuration (60min slots)"
        self.assertEqual(str(self.config), expected)


class WorkingShiftTestCase(TestCase):
    """Test cases for WorkingShift model."""
    
    def setUp(self):
        self.config = SlotConfiguration.objects.create(
            name="Test Configuration",
            slot_interval_minutes=60
        )
        self.shift = WorkingShift.objects.create(
            configuration=self.config,
            weekday=0,  # Monday
            start_time=time(9, 0),
            end_time=time(17, 0),
            is_working_day=True,
            max_bookings_per_slot=2
        )
    
    def test_working_shift_creation(self):
        """Test working shift creation."""
        self.assertEqual(self.shift.weekday, 0)
        self.assertEqual(self.shift.start_time, time(9, 0))
        self.assertEqual(self.shift.end_time, time(17, 0))
        self.assertTrue(self.shift.is_working_day)
        self.assertEqual(self.shift.max_bookings_per_slot, 2)
    
    def test_working_shift_str(self):
        """Test string representation."""
        expected = "Monday: 09:00:00 - 17:00:00"
        self.assertEqual(str(self.shift), expected)


class TimeSlotTestCase(TestCase):
    """Test cases for TimeSlot model."""
    
    def setUp(self):
        self.config = SlotConfiguration.objects.create(
            name="Test Configuration",
            slot_interval_minutes=60
        )
        self.slot = TimeSlot.objects.create(
            configuration=self.config,
            date=timezone.now().date() + timedelta(days=1),
            start_time=time(10, 0),
            end_time=time(11, 0),
            max_bookings=2
        )
    
    def test_time_slot_creation(self):
        """Test time slot creation."""
        self.assertEqual(self.slot.status, 'available')
        self.assertEqual(self.slot.current_bookings, 0)
        self.assertEqual(self.slot.max_bookings, 2)
    
    def test_is_available_property(self):
        """Test is_available property."""
        self.assertTrue(self.slot.is_available)
        
        # Make slot fully booked
        self.slot.current_bookings = 2
        self.slot.save()
        self.assertFalse(self.slot.is_available)
    
    def test_remaining_capacity_property(self):
        """Test remaining_capacity property."""
        self.assertEqual(self.slot.remaining_capacity, 2)
        
        self.slot.current_bookings = 1
        self.slot.save()
        self.assertEqual(self.slot.remaining_capacity, 1)
    
    def test_can_book_method(self):
        """Test can_book method."""
        self.assertTrue(self.slot.can_book(1))
        self.assertTrue(self.slot.can_book(2))
        self.assertFalse(self.slot.can_book(3))
        
        self.slot.current_bookings = 1
        self.slot.save()
        self.assertTrue(self.slot.can_book(1))
        self.assertFalse(self.slot.can_book(2))


class SlotGenerationServiceTestCase(TestCase):
    """Test cases for SlotGenerationService."""
    
    def setUp(self):
        self.config = SlotConfiguration.objects.create(
            name="Test Configuration",
            slot_interval_minutes=60,
            buffer_time_minutes=15
        )
        
        # Create working shift for Monday
        WorkingShift.objects.create(
            configuration=self.config,
            weekday=0,  # Monday
            start_time=time(9, 0),
            end_time=time(17, 0),
            is_working_day=True,
            max_bookings_per_slot=1
        )
    
    def test_generate_slots_for_working_day(self):
        """Test slot generation for a working day."""
        # Find next Monday
        today = timezone.now().date()
        days_ahead = 0 - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)
        
        generated_count = SlotGenerationService.generate_slots(
            configuration=self.config,
            start_date=next_monday,
            end_date=next_monday
        )
        
        # Should generate slots from 9:15 AM to 5:00 PM (with 15min buffer)
        # That's 7 hours and 45 minutes = 7 slots (9:15-10:15, 10:15-11:15, etc.)
        self.assertEqual(generated_count, 7)
        
        # Check that slots were created
        slots = TimeSlot.objects.filter(
            configuration=self.config,
            date=next_monday
        )
        self.assertEqual(slots.count(), 7)
        
        # Check first slot starts at 9:15 AM (with buffer)
        first_slot = slots.order_by('start_time').first()
        self.assertEqual(first_slot.start_time, time(9, 15))


class SlotAvailabilityServiceTestCase(TestCase):
    """Test cases for SlotAvailabilityService."""
    
    def setUp(self):
        self.config = SlotConfiguration.objects.create(
            name="Test Configuration",
            slot_interval_minutes=60
        )
        
        tomorrow = timezone.now().date() + timedelta(days=1)
        
        # Create available slot
        self.available_slot = TimeSlot.objects.create(
            configuration=self.config,
            date=tomorrow,
            start_time=time(10, 0),
            end_time=time(11, 0),
            status='available',
            max_bookings=2
        )
        
        # Create booked slot
        self.booked_slot = TimeSlot.objects.create(
            configuration=self.config,
            date=tomorrow,
            start_time=time(11, 0),
            end_time=time(12, 0),
            status='booked',
            current_bookings=1,
            max_bookings=1
        )
    
    def test_get_available_slots(self):
        """Test getting available slots."""
        tomorrow = timezone.now().date() + timedelta(days=1)
        available_slots = SlotAvailabilityService.get_available_slots(tomorrow)
        
        # Should only return the available slot
        self.assertEqual(len(available_slots), 1)
        self.assertEqual(available_slots[0].id, self.available_slot.id)
    
    def test_check_slot_availability(self):
        """Test checking specific slot availability."""
        # Test available slot
        is_available, message = SlotAvailabilityService.check_slot_availability(
            self.available_slot.id, quantity=1
        )
        self.assertTrue(is_available)
        self.assertEqual(message, "Slot is available")
        
        # Test booked slot
        is_available, message = SlotAvailabilityService.check_slot_availability(
            self.booked_slot.id, quantity=1
        )
        self.assertFalse(is_available)
        self.assertEqual(message, "Slot is booked")


class SlotBookingTestCase(TestCase):
    """Test cases for SlotBooking model."""
    
    def setUp(self):
        self.config = SlotConfiguration.objects.create(
            name="Test Configuration",
            slot_interval_minutes=60
        )
        
        self.slot = TimeSlot.objects.create(
            configuration=self.config,
            date=timezone.now().date() + timedelta(days=1),
            start_time=time(10, 0),
            end_time=time(11, 0),
            max_bookings=2
        )
        
        self.booking = SlotBooking.objects.create(
            time_slot=self.slot,
            order_id="ORDER123",
            customer_mobile="9876543210",
            customer_name="Test Customer",
            quantity=1,
            service_names=["Home Cleaning"]
        )
    
    def test_slot_booking_creation(self):
        """Test slot booking creation."""
        self.assertEqual(self.booking.order_id, "ORDER123")
        self.assertEqual(self.booking.customer_mobile, "9876543210")
        self.assertEqual(self.booking.customer_name, "Test Customer")
        self.assertEqual(self.booking.quantity, 1)
        self.assertEqual(self.booking.booking_status, 'pending')
    
    def test_slot_booking_str(self):
        """Test string representation."""
        expected = f"Booking ORDER123 - {self.slot}"
        self.assertEqual(str(self.booking), expected)
