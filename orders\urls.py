from django.urls import path
from . import views

app_name = 'orders'

urlpatterns = [
    # Order CRUD
    path('', views.OrderListCreateView.as_view(), name='order-list-create'),
    path('cod/', views.create_cod_order, name='create-cod-order'),

    # Dashboard (must come before order_number pattern)
    path('dashboard/', views.order_dashboard, name='order-dashboard'),

    # Order detail (this pattern matches any string, so it must come after specific patterns)
    path('<str:order_number>/', views.OrderDetailView.as_view(), name='order-detail'),

    # Order management
    path('<str:order_number>/status/', views.update_order_status, name='update-order-status'),
    path('<str:order_number>/accept/', views.accept_order, name='accept-order'),
    path('<str:order_number>/complete/', views.complete_order, name='complete-order'),
    path('<str:order_number>/hold/', views.hold_order, name='hold-order'),
    path('<str:order_number>/mark-incomplete/', views.mark_incomplete, name='mark-incomplete'),
    path('<str:order_number>/cancel/', views.cancel_order, name='cancel-order'),
    path('<str:order_number>/reschedule/', views.reschedule_order, name='reschedule-order'),
    path('<str:order_number>/assign-provider/', views.assign_provider, name='assign-provider'),
    path('<str:order_number>/payment/', views.update_payment_status, name='update-payment-status'),

    # Order tracking and history
    path('<str:order_number>/history/', views.OrderStatusHistoryView.as_view(), name='order-history'),

    # Admin AJAX endpoints
    path('admin/get-services/', views.get_services_for_admin, name='admin-get-services'),
    path('admin/calculate-tax/', views.calculate_tax_for_admin, name='admin-calculate-tax'),
    path('admin/apply-coupon/', views.apply_coupon_for_admin, name='admin-apply-coupon'),
    path('<str:order_number>/reschedules/', views.OrderRescheduleListView.as_view(), name='order-reschedules'),
]
