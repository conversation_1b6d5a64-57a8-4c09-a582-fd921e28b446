// components/Checkout.js - COMPLETE WORKING VERSION
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

const Checkout = () => {
  const router = useRouter();
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('razorpay');
  const [paymentConfig, setPaymentConfig] = useState(null);
  const [deliveryAddress, setDeliveryAddress] = useState({
    house_number: '',
    street_name: '',
    city: '',
    state: '',
    pincode: '',
  });
  const [customerNotes, setCustomerNotes] = useState('');
  const [scheduledDate, setScheduledDate] = useState('');
  const [timeSlot, setTimeSlot] = useState('');

  // API Base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

  // Get auth token
  const getAuthToken = () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('access_token');
    }
    return null;
  };

  // Get Cart
  const getCart = async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`${API_BASE_URL}/api/cart/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Get Cart Error:', error);
      throw error;
    }
  };

  // Get Payment Configuration
  const getPaymentConfiguration = async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`${API_BASE_URL}/api/payments/configuration/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Get Payment Config Error:', error);
      throw error;
    }
  };

  // Create COD Order
  const createCODOrder = async (orderData) => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`${API_BASE_URL}/api/orders/cod/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cart_id: orderData.cart_id,
          delivery_address: orderData.delivery_address,
          customer_notes: orderData.customer_notes || '',
          scheduled_date: orderData.scheduled_date,
          scheduled_time_slot: orderData.scheduled_time_slot,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        return {
          success: true,
          order: result.order,
          payment: result.payment,
          message: result.message,
        };
      } else {
        throw new Error(result.message || 'COD order creation failed');
      }
    } catch (error) {
      console.error('COD Order Error:', error);
      throw error;
    }
  };

  // Refresh Cart (if needed)
  const refreshCart = async () => {
    try {
      const cartData = await getCart();
      setCart(cartData);
    } catch (error) {
      console.error('Error refreshing cart:', error);
    }
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Load cart and payment configuration
      const [cartData, configData] = await Promise.all([
        getCart(),
        getPaymentConfiguration(),
      ]);
      
      setCart(cartData);
      setPaymentConfig(configData);
    } catch (error) {
      console.error('Error loading data:', error);
      alert('Error loading checkout data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    if (!cart || !cart.id) {
      alert('No items in cart');
      return false;
    }

    if (!deliveryAddress.house_number.trim()) {
      alert('House number is required');
      return false;
    }

    if (!deliveryAddress.street_name.trim()) {
      alert('Street name is required');
      return false;
    }

    if (!deliveryAddress.city.trim()) {
      alert('City is required');
      return false;
    }

    if (!deliveryAddress.state.trim()) {
      alert('State is required');
      return false;
    }

    if (!deliveryAddress.pincode.trim()) {
      alert('Pincode is required');
      return false;
    }

    return true;
  };

  const handleCODOrder = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const orderData = {
        cart_id: cart.id.toString(),
        delivery_address: deliveryAddress,
        customer_notes: customerNotes.trim(),
        scheduled_date: scheduledDate || null,
        scheduled_time_slot: timeSlot || null,
      };

      const result = await createCODOrder(orderData);

      // Show success message with COD details
      const message = `COD Order Placed Successfully!\n\nOrder Number: ${result.order.order_number}\nOriginal Amount: ₹${result.payment.original_amount}\nCOD Charges: ₹${result.payment.cod_charges}\nTotal Amount: ₹${result.payment.total_amount}`;
      
      alert(message);

      // Redirect to order confirmation or home page
      router.push('/orders'); // or wherever you want to redirect
    } catch (error) {
      console.error('COD Order Error:', error);
      alert('COD Order Failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePlaceOrder = () => {
    if (paymentMethod === 'cod') {
      handleCODOrder();
    } else {
      alert('Razorpay integration coming soon!');
    }
  };

  if (loading && !cart) {
    return <div className="loading">Loading checkout...</div>;
  }

  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <div className="empty-cart">
        <h2>Your cart is empty</h2>
        <p>Please add items to your cart before checkout.</p>
        <button onClick={() => router.push('/services')}>
          Browse Services
        </button>
      </div>
    );
  }

  return (
    <div className="checkout-container">
      <h1>Checkout</h1>

      {/* Cart Summary */}
      <div className="cart-summary">
        <h2>Order Summary</h2>
        <div className="cart-items">
          {cart.items && cart.items.map((item) => (
            <div key={item.id} className="cart-item">
              <span>{item.service_title}</span>
              <span>Qty: {item.quantity}</span>
              <span>₹{item.price_at_add}</span>
            </div>
          ))}
        </div>
        <div className="cart-total">
          <strong>Total: ₹{cart.total_amount}</strong>
        </div>
      </div>

      {/* Delivery Address */}
      <div className="delivery-address">
        <h2>Delivery Address</h2>
        <div className="address-form">
          <input
            type="text"
            placeholder="House Number *"
            value={deliveryAddress.house_number}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                house_number: e.target.value,
              })
            }
            required
          />
          <input
            type="text"
            placeholder="Street Name *"
            value={deliveryAddress.street_name}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                street_name: e.target.value,
              })
            }
            required
          />
          <input
            type="text"
            placeholder="City *"
            value={deliveryAddress.city}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                city: e.target.value,
              })
            }
            required
          />
          <input
            type="text"
            placeholder="State *"
            value={deliveryAddress.state}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                state: e.target.value,
              })
            }
            required
          />
          <input
            type="text"
            placeholder="Pincode *"
            value={deliveryAddress.pincode}
            onChange={(e) =>
              setDeliveryAddress({
                ...deliveryAddress,
                pincode: e.target.value,
              })
            }
            required
          />
        </div>
      </div>

      {/* Customer Notes */}
      <div className="customer-notes">
        <h2>Special Instructions (Optional)</h2>
        <textarea
          placeholder="Any special instructions for the service provider..."
          value={customerNotes}
          onChange={(e) => setCustomerNotes(e.target.value)}
          rows={3}
        />
      </div>

      {/* Payment Method */}
      <div className="payment-method">
        <h2>Payment Method</h2>
        <div className="payment-options">
          <label>
            <input
              type="radio"
              value="razorpay"
              checked={paymentMethod === 'razorpay'}
              onChange={(e) => setPaymentMethod(e.target.value)}
            />
            Online Payment (Coming Soon)
          </label>
          <label>
            <input
              type="radio"
              value="cod"
              checked={paymentMethod === 'cod'}
              onChange={(e) => setPaymentMethod(e.target.value)}
            />
            Cash on Delivery
            {paymentConfig && paymentConfig.cod_charge_percentage > 0 && (
              <span className="cod-charges">
                (Additional {paymentConfig.cod_charge_percentage}% charges apply)
              </span>
            )}
          </label>
        </div>
      </div>

      {/* Place Order Button */}
      <div className="place-order">
        <button
          onClick={handlePlaceOrder}
          disabled={loading}
          className="place-order-btn"
        >
          {loading
            ? 'Processing...'
            : paymentMethod === 'cod'
            ? 'Place COD Order'
            : 'Proceed to Payment'}
        </button>
      </div>

      <style jsx>{`
        .checkout-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }

        .cart-summary,
        .delivery-address,
        .customer-notes,
        .payment-method {
          margin-bottom: 30px;
          padding: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
        }

        .cart-item {
          display: flex;
          justify-content: space-between;
          padding: 10px 0;
          border-bottom: 1px solid #eee;
        }

        .address-form {
          display: grid;
          gap: 15px;
        }

        .address-form input {
          padding: 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 16px;
        }

        .customer-notes textarea {
          width: 100%;
          padding: 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 16px;
          resize: vertical;
        }

        .payment-options {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .payment-options label {
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;
        }

        .cod-charges {
          color: #666;
          font-size: 14px;
        }

        .place-order-btn {
          width: 100%;
          padding: 15px;
          background-color: #007bff;
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 18px;
          cursor: pointer;
          transition: background-color 0.3s;
        }

        .place-order-btn:hover:not(:disabled) {
          background-color: #0056b3;
        }

        .place-order-btn:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }

        .loading,
        .empty-cart {
          text-align: center;
          padding: 50px;
        }

        .cart-total {
          margin-top: 15px;
          padding-top: 15px;
          border-top: 2px solid #333;
          font-size: 18px;
        }
      `}</style>
    </div>
  );
};

export default Checkout;
