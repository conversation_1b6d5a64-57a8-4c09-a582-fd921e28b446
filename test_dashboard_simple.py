#!/usr/bin/env python3
"""
Simple test for dashboard API
"""
import requests
import json

def test_dashboard():
    # Login
    login_response = requests.post(
        "http://localhost:8000/api/auth/login/email/",
        json={"email": "<EMAIL>", "password": "admin123"}
    )
    
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.text}")
        return
    
    token = login_response.json()['tokens']['access']
    print(f"Login successful, token: {token[:50]}...")
    
    # Test dashboard
    headers = {"Authorization": f"Bearer {token}"}
    dashboard_response = requests.get(
        "http://localhost:8000/api/orders/dashboard/",
        headers=headers
    )
    
    print(f"Dashboard status: {dashboard_response.status_code}")
    if dashboard_response.status_code == 200:
        data = dashboard_response.json()
        print("Dashboard data:")
        print(json.dumps(data, indent=2))
    else:
        print(f"Dashboard error: {dashboard_response.text}")

if __name__ == "__main__":
    test_dashboard()
