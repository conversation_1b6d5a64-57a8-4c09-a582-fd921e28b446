<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Razorpay Payment Test - Home Services</title>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #3399cc;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .info-box {
            background-color: #e8f4fd;
            border: 1px solid #3399cc;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .config-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Razorpay Payment Test</h1>
            <p>Test your Razorpay integration for Home Services Platform</p>
        </div>

        <!-- Configuration Display -->
        <div class="form-group">
            <button onclick="loadConfiguration()">Load Payment Configuration</button>
            <div id="configDisplay" class="config-display" style="display: none;"></div>
        </div>

        <!-- Payment Form -->
        <form id="paymentForm">
            <div class="form-group">
                <label for="orderId">Order ID:</label>
                <input type="text" id="orderId" value="1" required>
            </div>

            <div class="form-group">
                <label for="amount">Amount (₹):</label>
                <input type="number" id="amount" value="500" step="0.01" required>
            </div>

            <div class="form-group">
                <label for="paymentMethod">Payment Method:</label>
                <select id="paymentMethod" required>
                    <option value="razorpay">Razorpay (Online)</option>
                    <option value="cod">Cash on Delivery</option>
                </select>
            </div>

            <div class="form-group">
                <label for="authToken">JWT Auth Token:</label>
                <input type="text" id="authToken" placeholder="Enter your JWT token" required>
            </div>

            <button type="button" onclick="calculatePartialPayment()">Calculate Partial Payment</button>
            <button type="button" onclick="initiatePayment()">Initiate Payment</button>
        </form>

        <!-- Results Display -->
        <div id="results"></div>

        <!-- Test Instructions -->
        <div class="info-box">
            <h3>📋 Test Instructions:</h3>
            <ol>
                <li><strong>Get JWT Token</strong>: Login through your authentication API to get a JWT token</li>
                <li><strong>Load Configuration</strong>: Click "Load Payment Configuration" to verify API connectivity</li>
                <li><strong>Test Partial Payment</strong>: Click "Calculate Partial Payment" to test service calculations</li>
                <li><strong>Test Razorpay</strong>: Select "Razorpay" and click "Initiate Payment"</li>
                <li><strong>Test COD</strong>: Select "Cash on Delivery" and click "Initiate Payment"</li>
            </ol>
        </div>

        <div class="info-box">
            <h3>💳 Razorpay Test Cards:</h3>
            <ul>
                <li><strong>Success</strong>: 4111 1111 1111 1111 (Any future expiry, any CVV)</li>
                <li><strong>Failure</strong>: 4000 0000 0000 0002 (Any future expiry, any CVV)</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api/payments';
        
        function showResult(message, type = 'info') {
            const results = document.getElementById('results');
            results.innerHTML = `<div class="info-box ${type}">${message}</div>`;
        }

        async function loadConfiguration() {
            try {
                const response = await fetch(`${API_BASE}/configuration/`);
                const data = await response.json();
                
                if (data.success) {
                    const configDisplay = document.getElementById('configDisplay');
                    configDisplay.style.display = 'block';
                    configDisplay.innerHTML = `<pre>${JSON.stringify(data.configuration, null, 2)}</pre>`;
                    showResult('✅ Configuration loaded successfully!', 'success');
                } else {
                    showResult('❌ Failed to load configuration', 'error');
                }
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function calculatePartialPayment() {
            const authToken = document.getElementById('authToken').value;
            if (!authToken) {
                showResult('❌ Please enter JWT token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/calculate-partial/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        items: [
                            { service_id: 1, quantity: 1 }
                        ]
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult(`
                        <h4>💰 Partial Payment Calculation:</h4>
                        <pre>${JSON.stringify(data.calculation, null, 2)}</pre>
                    `, 'success');
                } else {
                    showResult(`❌ Calculation failed: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function initiatePayment() {
            const orderId = document.getElementById('orderId').value;
            const amount = document.getElementById('amount').value;
            const paymentMethod = document.getElementById('paymentMethod').value;
            const authToken = document.getElementById('authToken').value;

            if (!authToken) {
                showResult('❌ Please enter JWT token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/initiate/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        order_id: orderId,
                        amount: amount,
                        payment_method: paymentMethod,
                        currency: 'INR'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    if (paymentMethod === 'razorpay') {
                        showResult('🚀 Razorpay order created! Opening checkout...', 'success');
                        openRazorpayCheckout(data, authToken);
                    } else {
                        showResult(`
                            <h4>💵 COD Payment Initiated:</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        `, 'success');
                    }
                } else {
                    showResult(`❌ Payment initiation failed: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        function openRazorpayCheckout(paymentData, authToken) {
            const options = {
                key: paymentData.razorpay_key_id,
                amount: paymentData.amount * 100, // Convert to paise
                currency: paymentData.currency,
                name: "Home Services",
                description: `Test Order ${paymentData.order_number}`,
                order_id: paymentData.razorpay_order_id,
                handler: async function(response) {
                    // Verify payment
                    try {
                        const verifyResponse = await fetch(`${API_BASE}/razorpay/callback/`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${authToken}`
                            },
                            body: JSON.stringify({
                                transaction_id: paymentData.transaction_id,
                                razorpay_payment_id: response.razorpay_payment_id,
                                razorpay_order_id: response.razorpay_order_id,
                                razorpay_signature: response.razorpay_signature
                            })
                        });

                        const result = await verifyResponse.json();
                        
                        if (result.success) {
                            showResult(`
                                <h4>🎉 Payment Successful!</h4>
                                <pre>${JSON.stringify(result, null, 2)}</pre>
                            `, 'success');
                        } else {
                            showResult(`❌ Payment verification failed: ${result.message}`, 'error');
                        }
                    } catch (error) {
                        showResult(`❌ Verification error: ${error.message}`, 'error');
                    }
                },
                prefill: {
                    name: "Test Customer",
                    email: "<EMAIL>",
                    contact: "9999999999"
                },
                theme: {
                    color: "#3399cc"
                }
            };

            const rzp = new Razorpay(options);
            rzp.open();
        }

        // Load configuration on page load
        window.onload = function() {
            loadConfiguration();
        };
    </script>
</body>
</html>
