#!/usr/bin/env python
"""
Create test data for Razorpay integration testing.
"""
import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from payments.models import PaymentConfiguration
from catalogue.models import Service, Category
from django.contrib.auth import get_user_model

User = get_user_model()

def create_payment_configuration():
    """Create or update payment configuration"""
    print("🔧 Creating Payment Configuration...")
    
    config = PaymentConfiguration.get_active_config()
    
    # Update with sample configuration
    config.razorpay_test_key_id = "rzp_test_SAMPLE_KEY_ID"
    config.razorpay_test_key_secret = "sample_test_secret_key"
    config.active_environment = "test"
    config.enable_razorpay = True
    config.enable_cod = True
    config.cod_charge_percentage = Decimal('2.50')
    config.cod_minimum_order = Decimal('100.00')
    config.save()
    
    print(f"✅ Payment Configuration Updated:")
    print(f"   - Environment: {config.active_environment}")
    print(f"   - Razorpay Enabled: {config.enable_razorpay}")
    print(f"   - COD Enabled: {config.enable_cod}")
    print(f"   - COD Charge: {config.cod_charge_percentage}%")
    print(f"   - COD Minimum: ₹{config.cod_minimum_order}")
    
    return config

def create_test_services():
    """Create test services with different partial payment configurations"""
    print("\n🛠️ Creating Test Services...")
    
    # Create test category
    category, created = Category.objects.get_or_create(
        name="Home Services",
        defaults={'description': 'Professional home services'}
    )
    if created:
        print(f"✅ Created category: {category.name}")
    
    # Service 1: Percentage-based partial payment
    service1, created = Service.objects.get_or_create(
        title="Home Cleaning Service",
        defaults={
            'category': category,
            'description': 'Professional home cleaning service with advance booking',
            'base_price': Decimal('1000.00'),
            'requires_partial_payment': True,
            'partial_payment_type': 'percentage',
            'partial_payment_value': Decimal('25.00'),  # 25%
            'partial_payment_description': 'Pay 25% advance to confirm your booking',
            'time_to_complete': timedelta(hours=2),  # 2 hours
            'is_active': True
        }
    )
    if created:
        print(f"✅ Created service: {service1.title}")
        print(f"   - Price: ₹{service1.base_price}")
        print(f"   - Partial Payment: {service1.partial_payment_value}% (₹{service1.get_partial_payment_amount()})")
    
    # Service 2: Fixed amount partial payment
    service2, created = Service.objects.get_or_create(
        title="AC Repair Service",
        defaults={
            'category': category,
            'description': 'Professional AC repair and maintenance service',
            'base_price': Decimal('800.00'),
            'requires_partial_payment': True,
            'partial_payment_type': 'fixed',
            'partial_payment_value': Decimal('200.00'),  # Fixed ₹200
            'partial_payment_description': 'Pay ₹200 advance for service confirmation',
            'time_to_complete': timedelta(hours=1, minutes=30),  # 1.5 hours
            'is_active': True
        }
    )
    if created:
        print(f"✅ Created service: {service2.title}")
        print(f"   - Price: ₹{service2.base_price}")
        print(f"   - Partial Payment: ₹{service2.partial_payment_value} (₹{service2.get_partial_payment_amount()})")
    
    # Service 3: No partial payment required
    service3, created = Service.objects.get_or_create(
        title="Plumbing Service",
        defaults={
            'category': category,
            'description': 'Quick plumbing fixes and repairs',
            'base_price': Decimal('500.00'),
            'requires_partial_payment': False,
            'time_to_complete': timedelta(hours=1),  # 1 hour
            'is_active': True
        }
    )
    if created:
        print(f"✅ Created service: {service3.title}")
        print(f"   - Price: ₹{service3.base_price}")
        print(f"   - Partial Payment: Not required")
    
    return [service1, service2, service3]

def display_admin_urls():
    """Display admin URLs for easy access"""
    print("\n📋 Admin Panel URLs:")
    print("=" * 50)
    print("🏠 Main Admin: http://127.0.0.1:8000/admin/")
    print("💳 Payment Configuration: http://127.0.0.1:8000/admin/payments/paymentconfiguration/")
    print("🛠️ Services: http://127.0.0.1:8000/admin/catalogue/service/")
    print("📊 Payment Transactions: http://127.0.0.1:8000/admin/payments/paymenttransaction/")
    print("💵 COD Payments: http://127.0.0.1:8000/admin/payments/codpayment/")
    print("🏦 Razorpay Payments: http://127.0.0.1:8000/admin/payments/razorpaypayment/")

def test_api_endpoints():
    """Test API endpoints"""
    print("\n🌐 Testing API Endpoints...")
    
    import requests
    
    try:
        # Test configuration endpoint
        response = requests.get('http://127.0.0.1:8000/api/payments/configuration/')
        if response.status_code == 200:
            data = response.json()
            print("✅ Payment Configuration API working:")
            print(f"   - Razorpay Enabled: {data['configuration']['enable_razorpay']}")
            print(f"   - COD Enabled: {data['configuration']['enable_cod']}")
            print(f"   - Environment: {data['configuration']['environment']}")
        else:
            print(f"❌ Configuration API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ API test failed: {e}")

def main():
    """Main function"""
    print("🚀 Creating Test Data for Razorpay Integration")
    print("=" * 60)
    
    try:
        # Create test data
        config = create_payment_configuration()
        services = create_test_services()
        
        # Test APIs
        test_api_endpoints()
        
        # Display admin URLs
        display_admin_urls()
        
        print("\n✅ Test data creation completed successfully!")
        print("\n📝 Next Steps:")
        print("1. Access the admin panel using the URLs above")
        print("2. Login with your superuser credentials")
        print("3. Configure Razorpay API keys in Payment Configuration")
        print("4. Test the payment APIs using Postman or the HTML test page")
        print("5. Integrate with your Next.js frontend")
        
        print("\n🧪 Testing Instructions:")
        print("- Use razorpay_test.html for quick frontend testing")
        print("- Import Razorpay_Payment_APIs.postman_collection.json in Postman")
        print("- Run python test_razorpay_integration.py for backend testing")
        
    except Exception as e:
        print(f"\n❌ Error creating test data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
