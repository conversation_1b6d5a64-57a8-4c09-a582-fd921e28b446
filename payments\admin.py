from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    PaymentConfiguration, PaymentTransaction, RazorpayPayment,
    CODPayment, PaymentRefund, PaymentWebhook
)


@admin.register(PaymentConfiguration)
class PaymentConfigurationAdmin(admin.ModelAdmin):
    """
    Admin interface for payment configuration.
    """
    list_display = [
        'active_environment', 'enable_razorpay', 'enable_cod',
        'razorpay_status', 'updated_at'
    ]
    fieldsets = (
        ('Environment Settings', {
            'fields': ('active_environment',),
            'description': 'Select which environment to use for payments'
        }),
        ('Payment Methods', {
            'fields': ('enable_razorpay', 'enable_cod'),
            'description': 'Enable or disable payment methods'
        }),
        ('Razorpay Test Configuration', {
            'fields': ('razorpay_test_key_id', 'razorpay_test_key_secret'),
            'classes': ('collapse',),
            'description': 'Test environment credentials for Razorpay'
        }),
        ('Razorpay Live Configuration', {
            'fields': ('razorpay_live_key_id', 'razorpay_live_key_secret'),
            'classes': ('collapse',),
            'description': 'Live environment credentials for Razorpay'
        }),
        ('Webhook Configuration', {
            'fields': ('razorpay_webhook_secret',),
            'classes': ('collapse',),
            'description': 'Webhook secret for payment notifications'
        }),
        ('COD Settings', {
            'fields': ('cod_charge_percentage', 'cod_minimum_order'),
            'description': 'Cash on Delivery configuration'
        }),
    )

    def has_add_permission(self, request):
        # Only allow one configuration
        return not PaymentConfiguration.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of payment configuration
        return False

    def razorpay_status(self, obj):
        """Show Razorpay configuration status"""
        credentials = obj.get_razorpay_credentials()
        if credentials['key_id'] and credentials['key_secret']:
            return format_html(
                '<span style="color: green;">✓ Configured ({})</span>',
                obj.get_active_environment_display()
            )
        else:
            return format_html(
                '<span style="color: red;">✗ Not Configured ({})</span>',
                obj.get_active_environment_display()
            )
    razorpay_status.short_description = 'Razorpay Status'


@admin.register(PaymentTransaction)
class PaymentTransactionAdmin(admin.ModelAdmin):
    """
    Admin interface for payment transactions.
    """
    list_display = [
        'transaction_id', 'order_number', 'user_email', 'payment_method',
        'amount', 'currency', 'status', 'created_at'
    ]
    list_filter = [
        'payment_method', 'status', 'currency', 'created_at'
    ]
    search_fields = [
        'transaction_id', 'order_number', 'user__email',
        'gateway_transaction_id', 'gateway_payment_id'
    ]
    readonly_fields = [
        'transaction_id', 'created_at', 'updated_at', 'completed_at',
        'refunded_at'
    ]

    fieldsets = (
        ('Transaction Details', {
            'fields': (
                'transaction_id', 'order_id', 'order_number', 'user',
                'payment_method', 'amount', 'currency', 'status'
            )
        }),
        ('Gateway Information', {
            'fields': (
                'gateway_transaction_id', 'gateway_payment_id',
                'gateway_signature', 'gateway_response'
            ),
            'classes': ('collapse',)
        }),
        ('Refund Information', {
            'fields': ('refund_amount',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': (
                'created_at', 'updated_at', 'completed_at',
                'refunded_at'
            ),
            'classes': ('collapse',)
        }),
        ('Failure Details', {
            'fields': ('failure_reason', 'failure_code'),
            'classes': ('collapse',)
        }),
    )

    def user_email(self, obj):
        """Get user email without cross-database join"""
        if obj.user_id:
            try:
                # Import here to avoid circular imports
                from django.contrib.auth import get_user_model
                User = get_user_model()
                user = User.objects.using('default').get(id=obj.user_id)
                return user.email
            except:
                return f"User ID: {obj.user_id}"
        return 'N/A'
    user_email.short_description = 'User Email'

    def has_add_permission(self, request):
        # Transactions should be created through API only
        return False


@admin.register(RazorpayPayment)
class RazorpayPaymentAdmin(admin.ModelAdmin):
    """
    Admin interface for Razorpay payment details.
    """
    list_display = [
        'razorpay_order_id', 'razorpay_payment_id', 'transaction_status',
        'webhook_verified', 'created_at'
    ]
    list_filter = ['webhook_verified', 'created_at']
    search_fields = [
        'razorpay_order_id', 'razorpay_payment_id', 'razorpay_signature'
    ]
    readonly_fields = ['created_at', 'updated_at']

    def transaction_status(self, obj):
        return obj.transaction.status
    transaction_status.short_description = 'Transaction Status'

    def has_add_permission(self, request):
        return False


@admin.register(CODPayment)
class CODPaymentAdmin(admin.ModelAdmin):
    """
    Admin interface for COD payment details.
    """
    list_display = [
        'transaction_id', 'collected_amount', 'is_collected',
        'collected_by_email', 'collected_at'
    ]
    list_filter = ['collected_at']
    search_fields = ['transaction__transaction_id']
    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        """Override queryset to avoid cross-database joins"""
        return super().get_queryset(request).select_related('transaction')

    def transaction_id(self, obj):
        return obj.transaction.transaction_id
    transaction_id.short_description = 'Transaction ID'

    def collected_by_email(self, obj):
        """Get collected by email without cross-database join"""
        if obj.collected_by_id:
            try:
                # Import here to avoid circular imports
                from django.contrib.auth import get_user_model
                User = get_user_model()
                user = User.objects.using('default').get(id=obj.collected_by_id)
                return user.email
            except:
                return f"User ID: {obj.collected_by_id}"
        return "N/A"
    collected_by_email.short_description = 'Collected By'

    def has_add_permission(self, request):
        return False

    def is_collected(self, obj):
        """Check if COD payment is collected"""
        return bool(obj.collected_at)
    is_collected.boolean = True
    is_collected.short_description = 'Collected'
