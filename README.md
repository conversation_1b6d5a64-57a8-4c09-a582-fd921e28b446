# Home Service Platform - Authentication Service

A comprehensive Django REST API authentication service for a home service platform with multi-user types (<PERSON>er, Provider, Staff), OTP-based authentication, rate limiting, and comprehensive admin features.

## 🛠️ Technical Stack

- **Backend**: Django 4.2.7, Django REST Framework
- **Database**: PostgreSQL
- **Cache**: Redis
- **Authentication**: JWT with Simple JWT
- **SMS Gateway**: MSG91
- **Rate Limiting**: Django Rate Limit
- **API Documentation**: DRF Spectacular (Swagger)

## 🚀 Quick Setup

### Prerequisites

1. **Python 3.8+**
2. **PostgreSQL 12+**
3. **Redis Server**
4. **MSG91 Account** (for SMS OTP)

### Installation Steps

1. **Setup Virtual Environment**

   **Option A: Automated Setup (Recommended)**
   ```bash
   # For Windows
   setup.bat

   # For Linux/Mac
   ./setup.sh
   ```

   **Option B: Manual Setup**
   ```bash
   # Create virtual environment
   python -m venv venv

   # Activate virtual environment
   # Windows:
   venv\Scripts\activate
   # Linux/Mac:
   source venv/bin/activate

   # Install dependencies
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

2. **Configure Environment Variables**
   
   Update the `.env` file with your actual values:
   ```env
   # Django Settings
   SECRET_KEY=your-actual-secret-key-here
   DEBUG=True
   ALLOWED_HOSTS=localhost,127.0.0.1

   # Database Settings (PostgreSQL)
   DB_NAME=auth_service
   DB_USER=postgres
   DB_PASSWORD=your-actual-db-password
   DB_HOST=localhost
   DB_PORT=5432

   # MSG91 SMS Gateway Settings
   MSG91_AUTH_KEY=your-actual-msg91-auth-key
   MSG91_TEMPLATE_ID=your-actual-template-id

   # Email Settings
   EMAIL_HOST_USER=<EMAIL>
   EMAIL_HOST_PASSWORD=your-email-password
   DEFAULT_FROM_EMAIL=<EMAIL>

   # CORS Settings
   CORS_ALLOWED_ORIGINS=http://localhost:3000,https://your-production-domain.com

   # Redis Settings
   REDIS_URL=redis://localhost:6379/1

   # JWT Settings
   JWT_ACCESS_TOKEN_LIFETIME=60
   JWT_REFRESH_TOKEN_LIFETIME=10080
   ```

3. **Setup PostgreSQL Database**
   ```sql
   -- Connect to PostgreSQL as superuser
   CREATE DATABASE auth_service;
   CREATE USER postgres WITH PASSWORD 'your-password';
   GRANT ALL PRIVILEGES ON DATABASE auth_service TO postgres;
   ```

4. **Setup Redis**
   - Install and start Redis server
   - Default configuration should work for development

5. **Run Database Migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

6. **Create Superuser**
   ```bash
   python manage.py createsuperuser
   ```

7. **Start Development Server**
   ```bash
   python manage.py runserver
   ```

## 📊 Database Models

### User Model
- **Fields**: email, mobile_number, name, user_type, is_verified, is_locked, profile_picture
- **User Types**: CUSTOMER, PROVIDER, STAFF
- **Authentication**: Email (Staff) or Mobile (Customer/Provider)

### Address Model
- **Fields**: user, address_type, street, city, state, zip_code, is_default
- **Types**: HOME, WORK, OTHER
- **Features**: Default address management

### FailedLoginAttempt Model
- **Fields**: user, mobile_number, email, ip_address, timestamp
- **Purpose**: Security tracking and account locking

## 🔐 Authentication System

### Authentication Backends
1. **EmailBackend**: Staff login with email/password
2. **MobileBackend**: Customer/Provider login with mobile/OTP

### JWT Configuration
- **Access Token**: 60 minutes
- **Refresh Token**: 7 days
- **Features**: Token rotation, blacklisting

## 📱 API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/register/mobile/` - Mobile registration
- `POST /api/auth/login/email/` - Staff email login
- `POST /api/auth/login/mobile/` - Mobile OTP login
- `POST /api/auth/logout/` - Logout

### OTP Management
- `POST /api/auth/otp/send/` - Send OTP
- `POST /api/auth/otp/verify/` - Verify OTP

### User Profile
- `GET /api/auth/profile/` - Get profile
- `PUT /api/auth/profile/` - Update profile
- `POST /api/auth/change-password/` - Change password

### Address Management
- `GET /api/auth/addresses/` - List addresses
- `POST /api/auth/addresses/` - Create address
- `GET /api/auth/addresses/{id}/` - Get address
- `PUT /api/auth/addresses/{id}/` - Update address
- `DELETE /api/auth/addresses/{id}/` - Delete address

### Admin Endpoints
- `GET /api/auth/admin/failed-attempts/` - View failed attempts
- `POST /api/auth/admin/unlock-account/` - Unlock account
- `POST /api/auth/admin/reset-rate-limit/` - Reset rate limits
- `GET /api/auth/admin/user-stats/` - User statistics

## 🛡️ Security Features

### Rate Limiting
- **Registration/Login**: 5 attempts per hour per IP
- **OTP Resend**: 3 attempts per hour per IP
- **Failed Attempts**: Account lockout after 5 failed attempts (30 min)

### Account Security
- **Account Locking**: Automatic lockout on failed attempts
- **IP Tracking**: Track failed attempts by IP address
- **OTP Verification**: SMS-based OTP using MSG91

## 🔧 Admin Features

### Enhanced Django Admin
- **Custom User Admin**: Enhanced interface with status indicators
- **Account Management**: View and manage locked accounts
- **Security Controls**: Reset rate limits, unlock accounts
- **Audit Trail**: Track failed login attempts

### Admin Actions
- Bulk unlock accounts
- Bulk verify users
- Bulk activate/deactivate users

## 📖 API Documentation

- **Swagger UI**: http://localhost:8000/api/docs/
- **OpenAPI Schema**: http://localhost:8000/api/schema/

## 🧪 Testing

```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test authentication

# Run with coverage
pip install coverage
coverage run --source='.' manage.py test
coverage report
```

## 🚀 Production Deployment

### Environment Variables
- Set `DEBUG=False`
- Use strong `SECRET_KEY`
- Configure proper `ALLOWED_HOSTS`
- Set up production database credentials
- Configure email settings for production

### Security Checklist
- [ ] Update all default passwords
- [ ] Configure HTTPS
- [ ] Set up proper CORS origins
- [ ] Configure rate limiting
- [ ] Set up monitoring and logging
- [ ] Regular security updates

## 📝 Usage Examples

### Register Customer
```bash
curl -X POST http://localhost:8000/api/auth/register/mobile/ \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "+919876543210",
    "name": "John Doe",
    "user_type": "CUSTOMER"
  }'
```

### Send OTP
```bash
curl -X POST http://localhost:8000/api/auth/otp/send/ \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "+919876543210"
  }'
```

### Login with OTP
```bash
curl -X POST http://localhost:8000/api/auth/login/mobile/ \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "+919876543210",
    "otp": "123456"
  }'
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For support and questions, please contact: <EMAIL>
