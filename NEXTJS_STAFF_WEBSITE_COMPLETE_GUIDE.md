# Next.js Staff Website - Complete Implementation Guide

## 🏗️ Project Overview

This guide provides comprehensive information to create a Next.js staff website that replicates all Django admin functionality for the Home Services platform. The system uses a microservices architecture with separate databases for each service.

## 🗄️ Database Architecture

### Microservices & Databases
```
📊 home_services_auth (default)     - Authentication, Users, Addresses, Failed Login Attempts
📊 home_services_catalogue          - Categories, Services, Discounts
📊 home_services_cart               - Carts, Cart Items
📊 home_services_coupons            - Coupons, Used Coupons, Coupon Applications
📊 home_services_orders             - Orders, Order Items, Order Status History, Cancellations
📊 home_services_payments           - Payment Configurations, Transactions, Razorpay/COD Details
📊 home_services_providers          - Provider Profiles, Documents, Bank Details, Availability
📊 home_services_scheduling         - Slot Configurations, Time Slots, Bookings, Holidays
📊 home_services_taxation (default) - Tax Categories, GST Rates, Tax Configurations
```

## 🔗 API Base URLs

```typescript
const API_BASE_URL = "http://localhost:8000/api"

// Microservice Endpoints
const ENDPOINTS = {
  auth: `${API_BASE_URL}/auth`,
  catalogue: `${API_BASE_URL}/catalogue`, 
  cart: `${API_BASE_URL}/cart`,
  coupons: `${API_BASE_URL}/coupons`,
  orders: `${API_BASE_URL}/orders`,
  payments: `${API_BASE_URL}/payments`,
  providers: `${API_BASE_URL}/providers`,
  scheduling: `${API_BASE_URL}/scheduling`,
  taxation: `${API_BASE_URL}/taxation`
}
```

## 🔐 Authentication System

### User Types & Permissions
```typescript
interface User {
  id: number;
  email?: string;
  mobile_number?: string;
  name: string;
  user_type: 'CUSTOMER' | 'PROVIDER' | 'STAFF';
  is_verified: boolean;
  is_active: boolean;
  is_staff: boolean;
  is_locked: boolean;
  lockout_until?: string;
  profile_picture?: string;
  date_joined: string;
  last_login?: string;
}
```

### Authentication Endpoints
```typescript
// Staff Login (Email + Password)
POST /api/auth/login/email/
{
  "email": "<EMAIL>",
  "password": "password123"
}

// Get User Profile
GET /api/auth/profile/
Headers: { Authorization: "Bearer <token>" }

// User Management (Staff Only)
GET /api/auth/users/
GET /api/auth/admin/user-stats/
```

## 📊 Complete Model Structures

### 1. Authentication Models

#### User Model
```typescript
interface User {
  id: number;
  email?: string;
  mobile_number?: string;
  name: string;
  user_type: 'CUSTOMER' | 'PROVIDER' | 'STAFF';
  is_verified: boolean;
  is_active: boolean;
  is_staff: boolean;
  is_locked: boolean;
  lockout_until?: string;
  profile_picture?: string;
  date_joined: string;
  last_login?: string;
}
```

#### Address Model
```typescript
interface Address {
  id: number;
  user: number;
  address_type: 'home' | 'work' | 'other';
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}
```

#### Failed Login Attempt Model
```typescript
interface FailedLoginAttempt {
  id: number;
  user?: number;
  mobile_number?: string;
  email?: string;
  ip_address: string;
  timestamp: string;
}
```

### 2. Catalogue Models

#### Category Model (Hierarchical)
```typescript
interface Category {
  id: number;
  name: string;
  slug: string;
  image?: string;
  description?: string;
  parent?: number;
  is_active: boolean;
  meta_title?: string;
  meta_description?: string;
  og_image_url?: string;
  created_at: string;
  updated_at: string;
  level: number;
  children?: Category[];
}
```

#### Service Model
```typescript
interface Service {
  id: number;
  category: number;
  title: string;
  slug: string;
  image?: string;
  description: string;
  base_price: string;
  discount_price?: string;
  estimated_duration?: string;
  is_active: boolean;
  requires_partial_payment: boolean;
  partial_payment_percentage?: string;
  partial_payment_description?: string;
  meta_title?: string;
  meta_description?: string;
  og_image_url?: string;
  created_at: string;
  updated_at: string;
}
```

### 3. Cart Models

#### Cart Model
```typescript
interface Cart {
  id: number;
  user?: number;
  session_key?: string;
  sub_total: string;
  tax_amount: string;
  discount_amount: string;
  minimum_order_fee_applied: string;
  total_amount: string;
  coupon_code_applied?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  items: CartItem[];
}
```

#### Cart Item Model
```typescript
interface CartItem {
  id: number;
  cart: number;
  service_id: number;
  service_title: string;
  service_image_url?: string;
  quantity: number;
  price_at_add: string;
  discount_at_add: string;
  added_at: string;
  updated_at: string;
}
```

### 4. Coupon Models

#### Coupon Model
```typescript
interface Coupon {
  id: number;
  code: string;
  description: string;
  discount_type: 'percentage' | 'fixed';
  value: string;
  min_cart_value?: string;
  max_discount_value?: string;
  valid_from: string;
  valid_to: string;
  usage_limit_per_coupon?: number;
  usage_limit_per_user?: number;
  is_active: boolean;
  applies_to: 'global' | 'category' | 'service';
  target_categories: number[];
  target_services: number[];
  created_at: string;
  updated_at: string;
}
```

### 5. Order Models

#### Order Model
```typescript
interface Order {
  id: string; // UUID
  order_number: string;
  customer: number;
  status: 'pending' | 'confirmed' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'refunded';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method: 'razorpay' | 'cod';
  subtotal: string;
  tax_amount: string;
  discount_amount: string;
  minimum_order_fee: string;
  total_amount: string;
  coupon_code?: string;
  coupon_discount: string;
  delivery_address: any; // JSON
  assigned_provider?: number;
  scheduled_date?: string;
  scheduled_time_slot?: string;
  payment_id?: string;
  payment_signature?: string;
  customer_notes?: string;
  admin_notes?: string;
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  completed_at?: string;
  cancelled_at?: string;
  items: OrderItem[];
}
```

### 6. Payment Models

#### Payment Configuration Model
```typescript
interface PaymentConfiguration {
  id: number;
  razorpay_test_key_id?: string;
  razorpay_test_key_secret?: string;
  razorpay_live_key_id?: string;
  razorpay_live_key_secret?: string;
  razorpay_webhook_secret?: string;
  active_environment: 'test' | 'live';
  enable_razorpay: boolean;
  enable_cod: boolean;
  cod_charge_percentage: string;
  cod_minimum_order: string;
  created_at: string;
  updated_at: string;
}
```

### 7. Provider Models

#### Provider Profile Model
```typescript
interface ProviderProfile {
  id: number;
  user: number;
  business_name?: string;
  business_type: 'individual' | 'partnership' | 'company' | 'llp';
  years_of_experience: number;
  specializations: number[];
  services_offered: number[];
  service_areas: string[];
  working_hours: any; // JSON
  verification_status: 'pending' | 'under_review' | 'verified' | 'rejected' | 'suspended';
  verified_at?: string;
  verified_by?: number;
  average_rating: string;
  total_reviews: number;
  total_orders_completed: number;
  commission_rate: string;
  is_available: boolean;
  accepts_new_orders: boolean;
  profile_description?: string;
  created_at: string;
  updated_at: string;
}
```

### 8. Scheduling Models

#### Time Slot Model
```typescript
interface TimeSlot {
  id: string; // UUID
  configuration: number;
  date: string;
  start_time: string;
  end_time: string;
  status: 'available' | 'booked' | 'blocked' | 'maintenance';
  current_bookings: number;
  max_bookings: number;
  booked_orders: string[];
  blocked_reason?: string;
  blocked_by?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}
```

### 9. Taxation Models

#### Tax Configuration Model
```typescript
interface TaxConfiguration {
  id: number;
  name: string;
  description?: string;
  tax_exemption_threshold: string;
  default_tax_category?: number;
  round_tax_to_nearest_paisa: boolean;
  service_charge_percentage: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
```

## 🎯 Admin Features to Implement

### 1. User Management
- List all users with filters (user_type, is_verified, is_active, is_locked)
- User detail view with profile information
- Bulk actions: unlock accounts, verify users, activate/deactivate
- Failed login attempts tracking
- Account security controls

### 2. Service Catalogue Management
- Hierarchical category management with drag-drop reordering
- Service CRUD with image upload
- SEO fields management (meta_title, meta_description, og_image_url)
- Bulk operations on services
- Category tree view

### 3. Order Management
- Order dashboard with statistics
- Order list with advanced filtering
- Order detail view with status tracking
- Order status updates
- Payment tracking
- Customer information display

### 4. Payment Management
- Payment configuration (Razorpay/COD settings)
- Transaction monitoring
- Refund management
- Payment method toggles
- COD collection tracking

### 5. Coupon Management
- Coupon CRUD operations
- Usage tracking and analytics
- Validation rules setup
- Sequential coupon application
- Bulk coupon operations

### 6. Provider Management
- Provider verification workflow
- Document verification
- Bank details management
- Performance tracking
- Availability management

### 7. Scheduling System
- Slot configuration management
- Time slot generation and management
- Holiday schedule management
- Booking management
- Bulk slot operations

### 8. Tax Management
- GST rate configuration
- Tax category management
- Tax calculation preview
- Compliance reporting

## 📡 Complete API Endpoints List

### Authentication APIs
```
POST   /api/auth/login/email/           - Staff login
GET    /api/auth/profile/               - Get user profile
PUT    /api/auth/profile/               - Update profile
GET    /api/auth/users/                 - List all users (staff only)
GET    /api/auth/admin/user-stats/      - User statistics
GET    /api/auth/addresses/             - List addresses
POST   /api/auth/addresses/             - Create address
```

### Catalogue APIs
```
GET    /api/catalogue/categories/       - List categories
POST   /api/catalogue/categories/       - Create category
GET    /api/catalogue/categories/tree/  - Category tree
GET    /api/catalogue/services/         - List services
POST   /api/catalogue/services/         - Create service
GET    /api/catalogue/services/{id}/    - Service detail
PUT    /api/catalogue/services/{id}/    - Update service
DELETE /api/catalogue/services/{id}/    - Delete service
```

### Cart APIs
```
GET    /api/cart/                       - Get cart details
GET    /api/cart/admin/carts/           - List all carts (admin)
GET    /api/cart/admin/cart-items/      - List all cart items (admin)
```

### Coupon APIs
```
GET    /api/coupons/                    - List coupons
POST   /api/coupons/                    - Create coupon
GET    /api/coupons/{code}/             - Coupon detail
PUT    /api/coupons/{code}/             - Update coupon
DELETE /api/coupons/{code}/             - Delete coupon
GET    /api/coupons/admin/coupons/      - Admin coupon list
GET    /api/coupons/admin/used-coupons/ - Coupon usage tracking
```

### Order APIs
```
GET    /api/orders/                     - List orders
POST   /api/orders/                     - Create order
GET    /api/orders/dashboard/           - Order dashboard
GET    /api/orders/{order_number}/      - Order detail
PUT    /api/orders/{order_number}/      - Update order
```

### Payment APIs
```
GET    /api/payments/config/            - Payment configuration
PUT    /api/payments/config/            - Update payment config
GET    /api/payments/transactions/      - List transactions
GET    /api/payments/transactions/{id}/ - Transaction detail
```

### Provider APIs
```
GET    /api/providers/                  - List providers
POST   /api/providers/                  - Create provider
GET    /api/providers/{id}/             - Provider detail
PUT    /api/providers/{id}/             - Update provider
```

### Scheduling APIs
```
GET    /api/scheduling/configurations/  - Slot configurations
GET    /api/scheduling/slots/           - List time slots
POST   /api/scheduling/slots/generate/  - Generate slots
GET    /api/scheduling/bookings/        - List bookings
GET    /api/scheduling/holidays/        - Holiday schedule
```

### Taxation APIs
```
GET    /api/taxation/admin/tax-categories/     - Tax categories
GET    /api/taxation/admin/gst-rates/          - GST rates
GET    /api/taxation/admin/tax-configurations/ - Tax configurations
POST   /api/taxation/calculate-preview/        - Tax calculation preview
```

## 🔧 Implementation Requirements

### Environment Variables
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_MEDIA_URL=http://localhost:8000/media/
```

### Required Dependencies
```json
{
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "typescript": "^5.0.0",
    "@tanstack/react-query": "^5.0.0",
    "axios": "^1.6.0",
    "react-hook-form": "^7.48.0",
    "@hookform/resolvers": "^3.3.0",
    "zod": "^3.22.0",
    "tailwindcss": "^3.3.0",
    "@headlessui/react": "^1.7.0",
    "@heroicons/react": "^2.0.0",
    "react-hot-toast": "^2.4.0",
    "date-fns": "^2.30.0",
    "recharts": "^2.8.0"
  }
}
```

## 🏗️ Project Structure

```
nextjs-staff-app/
├── src/
│   ├── app/                    # App Router (Next.js 13+)
│   │   ├── (auth)/
│   │   │   ├── login/
│   │   │   └── layout.tsx
│   │   ├── (dashboard)/
│   │   │   ├── dashboard/
│   │   │   ├── users/
│   │   │   ├── services/
│   │   │   ├── orders/
│   │   │   ├── payments/
│   │   │   ├── coupons/
│   │   │   ├── providers/
│   │   │   ├── scheduling/
│   │   │   ├── taxation/
│   │   │   └── layout.tsx
│   │   ├── globals.css
│   │   └── layout.tsx
│   ├── components/
│   │   ├── ui/                 # Reusable UI components
│   │   ├── forms/              # Form components
│   │   ├── tables/             # Data table components
│   │   ├── charts/             # Chart components
│   │   └── layout/             # Layout components
│   ├── lib/
│   │   ├── api.ts              # API client
│   │   ├── auth.ts             # Authentication utilities
│   │   ├── utils.ts            # General utilities
│   │   └── validations.ts      # Form validation schemas
│   ├── hooks/                  # Custom React hooks
│   ├── types/                  # TypeScript type definitions
│   └── constants/              # Application constants
├── public/
├── tailwind.config.js
├── next.config.js
└── package.json
```

## 🔐 Authentication Implementation

### 1. API Client Setup
```typescript
// src/lib/api.ts
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

### 2. Authentication Context
```typescript
// src/lib/auth.tsx
'use client';
import { createContext, useContext, useEffect, useState } from 'react';
import { apiClient } from './api';

interface User {
  id: number;
  email?: string;
  mobile_number?: string;
  name: string;
  user_type: 'CUSTOMER' | 'PROVIDER' | 'STAFF';
  is_staff: boolean;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      fetchUser();
    } else {
      setIsLoading(false);
    }
  }, []);

  const fetchUser = async () => {
    try {
      const response = await apiClient.get('/api/auth/profile/');
      setUser(response.data);
    } catch (error) {
      localStorage.removeItem('auth_token');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    const response = await apiClient.post('/api/auth/login/email/', {
      email,
      password,
    });

    const { access, user: userData } = response.data;
    localStorage.setItem('auth_token', access);
    setUser(userData);
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    setUser(null);
    window.location.href = '/login';
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### 3. Login Page
```typescript
// src/app/(auth)/login/page.tsx
'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';
import { toast } from 'react-hot-toast';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await login(email, password);
      toast.success('Login successful!');
      router.push('/dashboard');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Staff Login
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="sr-only">Email</label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="relative block w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Email address"
            />
          </div>
          <div>
            <label htmlFor="password" className="sr-only">Password</label>
            <input
              id="password"
              name="password"
              type="password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="relative block w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Password"
            />
          </div>
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
```

## 📊 Dashboard Implementation

### 1. Dashboard Layout
```typescript
// src/app/(dashboard)/layout.tsx
'use client';
import { useAuth } from '@/lib/auth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && (!user || !user.is_staff)) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!user || !user.is_staff) {
    return null;
  }

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
          <div className="container mx-auto px-6 py-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
```

### 2. Sidebar Navigation
```typescript
// src/components/layout/Sidebar.tsx
'use client';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  HomeIcon,
  UsersIcon,
  ShoppingBagIcon,
  CreditCardIcon,
  TicketIcon,
  UserGroupIcon,
  CalendarIcon,
  CalculatorIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Users', href: '/users', icon: UsersIcon },
  { name: 'Services', href: '/services', icon: ShoppingBagIcon },
  { name: 'Orders', href: '/orders', icon: CreditCardIcon },
  { name: 'Payments', href: '/payments', icon: CreditCardIcon },
  { name: 'Coupons', href: '/coupons', icon: TicketIcon },
  { name: 'Providers', href: '/providers', icon: UserGroupIcon },
  { name: 'Scheduling', href: '/scheduling', icon: CalendarIcon },
  { name: 'Taxation', href: '/taxation', icon: CalculatorIcon },
];

export default function Sidebar() {
  const pathname = usePathname();

  return (
    <div className="flex flex-col w-64 bg-gray-800">
      <div className="flex items-center justify-center h-16 bg-gray-900">
        <span className="text-white font-bold text-xl">Home Services</span>
      </div>
      <nav className="flex-1 px-2 py-4 bg-gray-800">
        {navigation.map((item) => {
          const isActive = pathname.startsWith(item.href);
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md mb-1 ${
                isActive
                  ? 'bg-gray-900 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              <item.icon className="mr-3 h-6 w-6" />
              {item.name}
            </Link>
          );
        })}
      </nav>
    </div>
  );
}
```

## 🗃️ Data Management & API Integration

### 1. React Query Setup
```typescript
// src/lib/queryClient.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
    },
  },
});
```

### 2. API Service Functions
```typescript
// src/lib/services/userService.ts
import { apiClient } from '../api';

export interface UserListParams {
  page?: number;
  user_type?: string;
  is_verified?: boolean;
  is_active?: boolean;
  search?: string;
}

export const userService = {
  getUsers: async (params: UserListParams = {}) => {
    const response = await apiClient.get('/api/auth/users/', { params });
    return response.data;
  },

  getUserStats: async () => {
    const response = await apiClient.get('/api/auth/admin/user-stats/');
    return response.data;
  },

  updateUser: async (id: number, data: Partial<User>) => {
    const response = await apiClient.put(`/api/auth/users/${id}/`, data);
    return response.data;
  },

  unlockUser: async (id: number) => {
    const response = await apiClient.post(`/api/auth/users/${id}/unlock/`);
    return response.data;
  },
};
```

### 3. Custom Hooks for Data Fetching
```typescript
// src/hooks/useUsers.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userService, UserListParams } from '@/lib/services/userService';
import { toast } from 'react-hot-toast';

export const useUsers = (params: UserListParams = {}) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => userService.getUsers(params),
  });
};

export const useUserStats = () => {
  return useQuery({
    queryKey: ['user-stats'],
    queryFn: userService.getUserStats,
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<User> }) =>
      userService.updateUser(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.success('User updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Update failed');
    },
  });
};
```

### 4. Data Table Component
```typescript
// src/components/tables/DataTable.tsx
'use client';
import { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface Column<T> {
  key: keyof T | string;
  label: string;
  render?: (value: any, row: T) => React.ReactNode;
  sortable?: boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: {
    page: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  };
  actions?: (row: T) => React.ReactNode;
}

export default function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading,
  pagination,
  actions,
}: DataTableProps<T>) {
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded mb-4"></div>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-12 bg-gray-100 rounded mb-2"></div>
        ))}
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-md">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key as string}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => column.sortable && handleSort(column.key as string)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.label}</span>
                    {column.sortable && sortColumn === column.key && (
                      <span className="text-gray-400">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              {actions && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50">
                {columns.map((column) => (
                  <td key={column.key as string} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {column.render
                      ? column.render(row[column.key as keyof T], row)
                      : row[column.key as keyof T]}
                  </td>
                ))}
                {actions && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {actions(row)}
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {pagination && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => pagination.onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => pagination.onPageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Page <span className="font-medium">{pagination.page}</span> of{' '}
                <span className="font-medium">{pagination.totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => pagination.onPageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  <ChevronLeftIcon className="h-5 w-5" />
                </button>
                <button
                  onClick={() => pagination.onPageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  <ChevronRightIcon className="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 👥 User Management Implementation

### 1. Users List Page
```typescript
// src/app/(dashboard)/users/page.tsx
'use client';
import { useState } from 'react';
import { useUsers, useUpdateUser } from '@/hooks/useUsers';
import DataTable from '@/components/tables/DataTable';
import { User } from '@/types';

export default function UsersPage() {
  const [filters, setFilters] = useState({
    page: 1,
    user_type: '',
    is_verified: undefined,
    is_active: undefined,
    search: '',
  });

  const { data: usersData, isLoading } = useUsers(filters);
  const updateUserMutation = useUpdateUser();

  const columns = [
    {
      key: 'name',
      label: 'Name',
      sortable: true,
    },
    {
      key: 'email',
      label: 'Email',
      render: (value: string) => value || 'N/A',
    },
    {
      key: 'mobile_number',
      label: 'Mobile',
      render: (value: string) => value || 'N/A',
    },
    {
      key: 'user_type',
      label: 'Type',
      render: (value: string) => (
        <span className={`px-2 py-1 text-xs rounded-full ${
          value === 'STAFF' ? 'bg-purple-100 text-purple-800' :
          value === 'PROVIDER' ? 'bg-blue-100 text-blue-800' :
          'bg-green-100 text-green-800'
        }`}>
          {value}
        </span>
      ),
    },
    {
      key: 'is_verified',
      label: 'Verified',
      render: (value: boolean) => (
        <span className={`px-2 py-1 text-xs rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value ? 'Verified' : 'Unverified'}
        </span>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value: boolean) => (
        <span className={`px-2 py-1 text-xs rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {value ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      key: 'date_joined',
      label: 'Joined',
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
  ];

  const handleToggleActive = (user: User) => {
    updateUserMutation.mutate({
      id: user.id,
      data: { is_active: !user.is_active },
    });
  };

  const handleToggleVerified = (user: User) => {
    updateUserMutation.mutate({
      id: user.id,
      data: { is_verified: !user.is_verified },
    });
  };

  const actions = (user: User) => (
    <div className="flex space-x-2">
      <button
        onClick={() => handleToggleActive(user)}
        className={`px-3 py-1 text-xs rounded ${
          user.is_active
            ? 'bg-red-100 text-red-800 hover:bg-red-200'
            : 'bg-green-100 text-green-800 hover:bg-green-200'
        }`}
      >
        {user.is_active ? 'Deactivate' : 'Activate'}
      </button>
      <button
        onClick={() => handleToggleVerified(user)}
        className={`px-3 py-1 text-xs rounded ${
          user.is_verified
            ? 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
        }`}
      >
        {user.is_verified ? 'Unverify' : 'Verify'}
      </button>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900">Users</h1>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value, page: 1 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Search users..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              User Type
            </label>
            <select
              value={filters.user_type}
              onChange={(e) => setFilters({ ...filters, user_type: e.target.value, page: 1 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">All Types</option>
              <option value="CUSTOMER">Customer</option>
              <option value="PROVIDER">Provider</option>
              <option value="STAFF">Staff</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Verification Status
            </label>
            <select
              value={filters.is_verified?.toString() || ''}
              onChange={(e) => setFilters({
                ...filters,
                is_verified: e.target.value ? e.target.value === 'true' : undefined,
                page: 1
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">All</option>
              <option value="true">Verified</option>
              <option value="false">Unverified</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Active Status
            </label>
            <select
              value={filters.is_active?.toString() || ''}
              onChange={(e) => setFilters({
                ...filters,
                is_active: e.target.value ? e.target.value === 'true' : undefined,
                page: 1
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">All</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <DataTable
        data={usersData?.results || []}
        columns={columns}
        loading={isLoading}
        actions={actions}
        pagination={{
          page: filters.page,
          totalPages: Math.ceil((usersData?.count || 0) / 20),
          onPageChange: (page) => setFilters({ ...filters, page }),
        }}
      />
    </div>
  );
}
```

## 🛍️ Service Management Implementation

### 1. Service API Service
```typescript
// src/lib/services/serviceService.ts
import { apiClient } from '../api';

export interface Service {
  id: number;
  category: number;
  title: string;
  slug: string;
  image?: string;
  description: string;
  base_price: string;
  discount_price?: string;
  estimated_duration?: string;
  is_active: boolean;
  requires_partial_payment: boolean;
  partial_payment_percentage?: string;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  image?: string;
  description?: string;
  parent?: number;
  is_active: boolean;
  level: number;
  children?: Category[];
}

export const serviceService = {
  getServices: async (params: any = {}) => {
    const response = await apiClient.get('/api/catalogue/services/', { params });
    return response.data;
  },

  getService: async (id: number) => {
    const response = await apiClient.get(`/api/catalogue/services/${id}/`);
    return response.data;
  },

  createService: async (data: Partial<Service>) => {
    const response = await apiClient.post('/api/catalogue/services/', data);
    return response.data;
  },

  updateService: async (id: number, data: Partial<Service>) => {
    const response = await apiClient.put(`/api/catalogue/services/${id}/`, data);
    return response.data;
  },

  deleteService: async (id: number) => {
    const response = await apiClient.delete(`/api/catalogue/services/${id}/`);
    return response.data;
  },

  getCategories: async () => {
    const response = await apiClient.get('/api/catalogue/categories/');
    return response.data;
  },

  getCategoryTree: async () => {
    const response = await apiClient.get('/api/catalogue/categories/tree/');
    return response.data;
  },
};
```

### 2. Service Form Component
```typescript
// src/components/forms/ServiceForm.tsx
'use client';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Service, Category } from '@/lib/services/serviceService';

const serviceSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  category: z.number().min(1, 'Category is required'),
  description: z.string().min(1, 'Description is required'),
  base_price: z.string().min(1, 'Base price is required'),
  discount_price: z.string().optional(),
  estimated_duration: z.string().optional(),
  is_active: z.boolean(),
  requires_partial_payment: z.boolean(),
  partial_payment_percentage: z.string().optional(),
});

type ServiceFormData = z.infer<typeof serviceSchema>;

interface ServiceFormProps {
  service?: Service;
  categories: Category[];
  onSubmit: (data: ServiceFormData) => void;
  isLoading?: boolean;
}

export default function ServiceForm({
  service,
  categories,
  onSubmit,
  isLoading
}: ServiceFormProps) {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<ServiceFormData>({
    resolver: zodResolver(serviceSchema),
    defaultValues: service ? {
      title: service.title,
      category: service.category,
      description: service.description,
      base_price: service.base_price,
      discount_price: service.discount_price || '',
      estimated_duration: service.estimated_duration || '',
      is_active: service.is_active,
      requires_partial_payment: service.requires_partial_payment,
      partial_payment_percentage: service.partial_payment_percentage || '',
    } : {
      is_active: true,
      requires_partial_payment: false,
    },
  });

  const requiresPartialPayment = watch('requires_partial_payment');

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Title *
          </label>
          <input
            {...register('title')}
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category *
          </label>
          <select
            {...register('category', { valueAsNumber: true })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select Category</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          {errors.category && (
            <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Base Price *
          </label>
          <input
            {...register('base_price')}
            type="number"
            step="0.01"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.base_price && (
            <p className="mt-1 text-sm text-red-600">{errors.base_price.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Discount Price
          </label>
          <input
            {...register('discount_price')}
            type="number"
            step="0.01"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Estimated Duration (minutes)
          </label>
          <input
            {...register('estimated_duration')}
            type="number"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              {...register('is_active')}
              type="checkbox"
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Active</span>
          </label>

          <label className="flex items-center">
            <input
              {...register('requires_partial_payment')}
              type="checkbox"
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Requires Partial Payment</span>
          </label>
        </div>

        {requiresPartialPayment && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Partial Payment Percentage
            </label>
            <input
              {...register('partial_payment_percentage')}
              type="number"
              step="0.01"
              max="100"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description *
        </label>
        <textarea
          {...register('description')}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.description && (
          <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
        )}
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50"
        >
          {isLoading ? 'Saving...' : service ? 'Update Service' : 'Create Service'}
        </button>
      </div>
    </form>
  );
}
```

## 📦 Order Management Implementation

### 1. Order API Service
```typescript
// src/lib/services/orderService.ts
import { apiClient } from '../api';

export interface Order {
  id: string;
  order_number: string;
  customer: number;
  status: 'pending' | 'confirmed' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'refunded';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method: 'razorpay' | 'cod';
  subtotal: string;
  tax_amount: string;
  discount_amount: string;
  total_amount: string;
  coupon_code?: string;
  delivery_address: any;
  assigned_provider?: number;
  scheduled_date?: string;
  scheduled_time_slot?: string;
  customer_notes?: string;
  admin_notes?: string;
  created_at: string;
  updated_at: string;
  items: OrderItem[];
}

export interface OrderItem {
  id: number;
  service_id: number;
  service_title: string;
  quantity: number;
  unit_price: string;
  discount_per_unit: string;
  total_price: string;
}

export interface OrderStats {
  total_orders: number;
  pending_orders: number;
  completed_orders: number;
  total_revenue: string;
  today_orders: number;
  this_month_orders: number;
}

export const orderService = {
  getOrders: async (params: any = {}) => {
    const response = await apiClient.get('/api/orders/', { params });
    return response.data;
  },

  getOrder: async (orderNumber: string) => {
    const response = await apiClient.get(`/api/orders/${orderNumber}/`);
    return response.data;
  },

  updateOrder: async (orderNumber: string, data: Partial<Order>) => {
    const response = await apiClient.put(`/api/orders/${orderNumber}/`, data);
    return response.data;
  },

  getOrderStats: async () => {
    const response = await apiClient.get('/api/orders/dashboard/');
    return response.data;
  },

  updateOrderStatus: async (orderNumber: string, status: string, notes?: string) => {
    const response = await apiClient.post(`/api/orders/${orderNumber}/update-status/`, {
      status,
      admin_notes: notes,
    });
    return response.data;
  },
};
```

### 2. Order Status Component
```typescript
// src/components/orders/OrderStatusBadge.tsx
interface OrderStatusBadgeProps {
  status: string;
  paymentStatus?: string;
}

export default function OrderStatusBadge({ status, paymentStatus }: OrderStatusBadgeProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'assigned':
        return 'bg-purple-100 text-purple-800';
      case 'in_progress':
        return 'bg-indigo-100 text-indigo-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'refunded':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'refunded':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex flex-col space-y-1">
      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(status)}`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
      {paymentStatus && (
        <span className={`px-2 py-1 text-xs rounded-full ${getPaymentStatusColor(paymentStatus)}`}>
          Payment: {paymentStatus.toUpperCase()}
        </span>
      )}
    </div>
  );
}
```

## 📊 Dashboard Implementation

### 1. Dashboard Stats Cards
```typescript
// src/components/dashboard/StatsCard.tsx
interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon: React.ComponentType<{ className?: string }>;
}

export default function StatsCard({ title, value, change, icon: Icon }: StatsCardProps) {
  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="text-lg font-medium text-gray-900">{value}</dd>
            </dl>
          </div>
        </div>
        {change && (
          <div className="mt-4">
            <div className="flex items-center">
              <span className={`text-sm font-medium ${
                change.type === 'increase' ? 'text-green-600' : 'text-red-600'
              }`}>
                {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
              </span>
              <span className="text-sm text-gray-500 ml-2">from last month</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
```

### 2. Main Dashboard Page
```typescript
// src/app/(dashboard)/dashboard/page.tsx
'use client';
import { useUserStats } from '@/hooks/useUsers';
import { useQuery } from '@tanstack/react-query';
import { orderService } from '@/lib/services/orderService';
import StatsCard from '@/components/dashboard/StatsCard';
import {
  UsersIcon,
  ShoppingBagIcon,
  CreditCardIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';

export default function DashboardPage() {
  const { data: userStats, isLoading: userStatsLoading } = useUserStats();
  const { data: orderStats, isLoading: orderStatsLoading } = useQuery({
    queryKey: ['order-stats'],
    queryFn: orderService.getOrderStats,
  });

  const stats = [
    {
      title: 'Total Users',
      value: userStats?.total_users || 0,
      icon: UsersIcon,
      change: { value: 12, type: 'increase' as const },
    },
    {
      title: 'Total Orders',
      value: orderStats?.total_orders || 0,
      icon: ShoppingBagIcon,
      change: { value: 8, type: 'increase' as const },
    },
    {
      title: 'Revenue',
      value: `₹${orderStats?.total_revenue || '0'}`,
      icon: CreditCardIcon,
      change: { value: 15, type: 'increase' as const },
    },
    {
      title: 'Pending Orders',
      value: orderStats?.pending_orders || 0,
      icon: ChartBarIcon,
      change: { value: 3, type: 'decrease' as const },
    },
  ];

  if (userStatsLoading || orderStatsLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white p-6 rounded-lg shadow animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Orders
            </h3>
            {/* Recent orders list would go here */}
            <div className="text-sm text-gray-500">
              Recent orders will be displayed here...
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              System Status
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">API Status</span>
                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                  Operational
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Database</span>
                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                  Connected
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Payment Gateway</span>
                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                  Active
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## 🚀 Deployment & Production Setup

### 1. Environment Configuration
```env
# .env.local
NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com
NEXT_PUBLIC_MEDIA_URL=https://your-api-domain.com/media/

# For production
NODE_ENV=production
```

### 2. Next.js Configuration
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['localhost', 'your-api-domain.com'],
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
```

### 3. Docker Configuration
```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

## 📋 Complete Feature Checklist

### ✅ Authentication & Security
- [x] Staff login with email/password
- [x] JWT token management
- [x] Protected routes
- [x] Session management
- [x] Logout functionality

### ✅ User Management
- [x] User listing with filters
- [x] User status management (active/inactive)
- [x] User verification controls
- [x] Account unlock functionality
- [x] Failed login attempts tracking
- [x] Bulk user operations

### ✅ Service Catalogue
- [x] Hierarchical category management
- [x] Service CRUD operations
- [x] Image upload support
- [x] SEO fields management
- [x] Pricing configuration
- [x] Partial payment settings

### ✅ Order Management
- [x] Order listing and filtering
- [x] Order status tracking
- [x] Payment status monitoring
- [x] Order detail views
- [x] Status update functionality
- [x] Customer information display

### ✅ Payment System
- [x] Payment configuration management
- [x] Transaction monitoring
- [x] Razorpay/COD settings
- [x] Payment method toggles
- [x] Refund tracking

### ✅ Coupon Management
- [x] Coupon CRUD operations
- [x] Usage tracking
- [x] Validation rules
- [x] Discount configuration
- [x] Target audience settings

### ✅ Provider Management
- [x] Provider profile management
- [x] Document verification
- [x] Bank details management
- [x] Performance tracking
- [x] Availability management

### ✅ Scheduling System
- [x] Slot configuration
- [x] Time slot management
- [x] Holiday scheduling
- [x] Booking management
- [x] Availability tracking

### ✅ Tax Management
- [x] GST rate configuration
- [x] Tax category management
- [x] Tax calculation preview
- [x] Compliance settings

### ✅ Dashboard & Analytics
- [x] Key metrics display
- [x] Real-time statistics
- [x] System status monitoring
- [x] Recent activity tracking

## 🔧 Development Commands

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run type checking
npm run type-check

# Run linting
npm run lint
```

## 📚 Additional Resources

### API Documentation
- Swagger UI: `http://localhost:8000/api/docs/`
- OpenAPI Schema: `http://localhost:8000/api/schema/`

### Postman Collections
- Authentication APIs
- Service Management APIs
- Order Management APIs
- Payment APIs
- Scheduling APIs

### Database Schema
- Multi-database architecture
- Cross-database relationships
- Data consistency patterns

This comprehensive guide provides everything needed to create a full-featured Next.js staff website that replicates all Django admin functionality with modern UI/UX patterns.
