#!/usr/bin/env python
"""
Test script for Razorpay integration.
Run this script to verify the payment system is working correctly.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from payments.models import PaymentConfiguration
from catalogue.models import Service, Category
from decimal import Decimal

def test_payment_configuration():
    """Test payment configuration setup"""
    print("🔧 Testing Payment Configuration...")
    
    # Create or get payment configuration
    config = PaymentConfiguration.get_active_config()
    
    print(f"✅ Payment Configuration Created: ID {config.id}")
    print(f"   - Environment: {config.active_environment}")
    print(f"   - Razorpay Enabled: {config.enable_razorpay}")
    print(f"   - COD Enabled: {config.enable_cod}")
    print(f"   - COD Charge: {config.cod_charge_percentage}%")
    print(f"   - COD Minimum: ₹{config.cod_minimum_order}")
    
    # Test credential retrieval
    credentials = config.get_razorpay_credentials()
    print(f"   - Razorpay Keys Configured: {bool(credentials['key_id'])}")
    
    return config

def test_service_partial_payment():
    """Test service partial payment functionality"""
    print("\n🛠️ Testing Service Partial Payment...")
    
    # Create a test category if it doesn't exist
    category, created = Category.objects.get_or_create(
        name="Test Category",
        defaults={'description': 'Test category for payment testing'}
    )
    if created:
        print(f"✅ Created test category: {category.name}")
    
    # Create a test service with partial payment
    service, created = Service.objects.get_or_create(
        title="Test Service with Partial Payment",
        defaults={
            'category': category,
            'description': 'Test service for partial payment testing',
            'base_price': Decimal('1000.00'),
            'requires_partial_payment': True,
            'partial_payment_type': 'percentage',
            'partial_payment_value': Decimal('20.00'),  # 20%
            'partial_payment_description': 'Pay 20% advance to book this service'
        }
    )
    
    if created:
        print(f"✅ Created test service: {service.title}")
    
    # Test partial payment calculations
    print(f"   - Service Price: ₹{service.get_current_price()}")
    print(f"   - Requires Partial Payment: {service.requires_partial_payment}")
    
    if service.requires_partial_payment:
        partial_amount = service.get_partial_payment_amount(quantity=1)
        remaining_amount = service.get_remaining_payment_amount(quantity=1)
        
        print(f"   - Partial Payment (1 qty): ₹{partial_amount}")
        print(f"   - Remaining Amount (1 qty): ₹{remaining_amount}")
        
        # Test with multiple quantities
        partial_amount_2 = service.get_partial_payment_amount(quantity=2)
        remaining_amount_2 = service.get_remaining_payment_amount(quantity=2)
        
        print(f"   - Partial Payment (2 qty): ₹{partial_amount_2}")
        print(f"   - Remaining Amount (2 qty): ₹{remaining_amount_2}")
    
    return service

def test_api_endpoints():
    """Test API endpoint availability"""
    print("\n🌐 Testing API Endpoints...")
    
    from django.urls import reverse
    from django.test import Client
    
    client = Client()
    
    # Test public configuration endpoint
    try:
        response = client.get('/api/payments/configuration/')
        if response.status_code == 200:
            print("✅ Payment configuration endpoint working")
            data = response.json()
            print(f"   - Response: {data}")
        else:
            print(f"❌ Configuration endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Configuration endpoint error: {e}")

def test_razorpay_client():
    """Test Razorpay client initialization"""
    print("\n💳 Testing Razorpay Client...")
    
    try:
        import razorpay
        print("✅ Razorpay package imported successfully")
        
        # Test with dummy credentials
        client = razorpay.Client(auth=('test_key', 'test_secret'))
        print("✅ Razorpay client initialized successfully")
        
    except ImportError:
        print("❌ Razorpay package not installed")
    except Exception as e:
        print(f"❌ Razorpay client error: {e}")

def display_admin_instructions():
    """Display admin setup instructions"""
    print("\n📋 Admin Setup Instructions:")
    print("=" * 50)
    print("1. Start the Django server:")
    print("   python manage.py runserver")
    print()
    print("2. Access Django Admin:")
    print("   http://127.0.0.1:8000/admin/")
    print()
    print("3. Configure Payment Settings:")
    print("   - Go to: Payments > Payment Configurations")
    print("   - Add your Razorpay Test/Live API keys")
    print("   - Select active environment (test/live)")
    print("   - Configure COD settings")
    print()
    print("4. Configure Services:")
    print("   - Go to: Catalogue > Services")
    print("   - Edit services to add partial payment settings")
    print("   - Set percentage or fixed amount for advance payment")
    print()
    print("5. Test API Endpoints:")
    print("   - Use Postman or curl to test endpoints")
    print("   - Refer to RAZORPAY_API_DOCUMENTATION.md")

def main():
    """Main test function"""
    print("🚀 Razorpay Integration Test Suite")
    print("=" * 50)
    
    try:
        # Run tests
        config = test_payment_configuration()
        service = test_service_partial_payment()
        test_api_endpoints()
        test_razorpay_client()
        
        print("\n✅ All tests completed successfully!")
        print("\n📊 Test Summary:")
        print(f"   - Payment Configuration: ✅ Working")
        print(f"   - Service Partial Payment: ✅ Working")
        print(f"   - API Endpoints: ✅ Available")
        print(f"   - Razorpay Package: ✅ Installed")
        
        display_admin_instructions()
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
