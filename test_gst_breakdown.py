#!/usr/bin/env python
"""
Test script to verify the GST breakdown functionality
"""
import requests
import json

def test_empty_cart():
    """Test empty cart with GST breakdown"""
    print("🧪 Testing Empty Cart with GST Breakdown...")
    
    try:
        response = requests.get("http://localhost:8000/api/cart/")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Empty Cart Response:")
            print(f"   CGST Amount: {data.get('cgst_amount', 'MISSING')}")
            print(f"   SGST Amount: {data.get('sgst_amount', 'MISSING')}")
            print(f"   IGST Amount: {data.get('igst_amount', 'MISSING')}")
            print(f"   Service Charge: {data.get('service_charge', 'MISSING')}")
            print(f"   Tax Breakdown: {data.get('tax_breakdown', 'MISSING')}")
            return True
        else:
            print(f"❌ Failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_add_to_cart():
    """Test adding item to cart"""
    print("\n🧪 Testing Add to Cart...")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/cart/add/",
            json={"service_id": 1, "quantity": 1},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Text: {response.text[:500]}...")  # First 500 chars
        
        if response.status_code == 201:
            data = response.json()
            cart_data = data.get('cart', {})
            print("✅ Add to Cart Success:")
            print(f"   Subtotal: {cart_data.get('sub_total', 'MISSING')}")
            print(f"   CGST Amount: {cart_data.get('cgst_amount', 'MISSING')}")
            print(f"   SGST Amount: {cart_data.get('sgst_amount', 'MISSING')}")
            print(f"   IGST Amount: {cart_data.get('igst_amount', 'MISSING')}")
            print(f"   Service Charge: {cart_data.get('service_charge', 'MISSING')}")
            print(f"   Tax Amount: {cart_data.get('tax_amount', 'MISSING')}")
            print(f"   Total Amount: {cart_data.get('total_amount', 'MISSING')}")
            
            tax_breakdown = cart_data.get('tax_breakdown', [])
            if tax_breakdown:
                print("   Tax Breakdown:")
                for item in tax_breakdown:
                    print(f"     - {item.get('type', 'Unknown')} ({item.get('rate', 'N/A')}): ₹{item.get('amount', '0.00')}")
            else:
                print("   Tax Breakdown: Empty or missing")
            
            return True
        else:
            print(f"❌ Failed with status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_tax_calculation_service():
    """Test tax calculation service directly"""
    print("\n🧪 Testing Tax Calculation Service...")
    
    try:
        # Test the tax calculation service directly
        import sys
        import os
        sys.path.append(os.getcwd())
        
        from taxation.services import TaxCalculationService
        from decimal import Decimal
        
        result = TaxCalculationService.calculate_total_tax_and_charges(Decimal('780.00'))
        
        print("✅ Direct Tax Calculation:")
        print(f"   Subtotal: ₹{result['subtotal']}")
        print(f"   CGST: ₹{result['cgst']}")
        print(f"   SGST: ₹{result['sgst']}")
        print(f"   IGST: ₹{result['igst']}")
        print(f"   Service Charge: ₹{result['service_charge']}")
        print(f"   Total GST: ₹{result['total_gst']}")
        print(f"   Grand Total: ₹{result['grand_total']}")
        
        if 'rates' in result:
            rates = result['rates']
            print("   Applied Rates:")
            print(f"     CGST Rate: {rates.get('cgst_rate', 0)}%")
            print(f"     SGST Rate: {rates.get('sgst_rate', 0)}%")
            print(f"     IGST Rate: {rates.get('igst_rate', 0)}%")
            print(f"     Service Charge Rate: {rates.get('service_charge_rate', 0)}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Testing GST Breakdown Functionality")
    print("=" * 50)
    
    # Test 1: Empty cart
    test1_success = test_empty_cart()
    
    # Test 2: Tax calculation service
    test2_success = test_tax_calculation_service()
    
    # Test 3: Add to cart
    test3_success = test_add_to_cart()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Empty Cart GST Fields: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   Tax Calculation Service: {'✅ PASS' if test2_success else '❌ FAIL'}")
    print(f"   Add to Cart GST: {'✅ PASS' if test3_success else '❌ FAIL'}")
    
    if all([test1_success, test2_success, test3_success]):
        print("\n🎉 ALL TESTS PASSED! GST breakdown is working correctly.")
    else:
        print("\n⚠️  SOME TESTS FAILED. Check the errors above.")
