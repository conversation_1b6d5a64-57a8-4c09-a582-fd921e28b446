# 🛒 Cart & Order Issues - Complete Solution Guide

## 🔍 **Current Issues Identified**

### **1. Cart Not Found Error (404)**
```
Backend error: 404 {"success":false,"message":"Cart not found"}
```

### **2. Connection Issues**
```
Proxy error: TypeError: fetch failed ... ECONNREFUSED
```

### **3. Payment Button Not Enabling**
- Online payment button not enabling
- COD (Cash on Delivery) showing errors

## 🛠️ **Root Cause Analysis**

### **Cart Issues:**
1. **Invalid cart_id** - Frontend sending non-existent cart ID
2. **User mismatch** - <PERSON>t belongs to different user
3. **Inactive cart** - Cart marked as inactive
4. **Empty cart** - Cart exists but has no items

### **Connection Issues:**
1. **Django server stopped** - Backend not running
2. **Port conflicts** - Server running on different port
3. **Network issues** - Proxy configuration problems

## 🔧 **Step-by-Step Solution**

### **Step 1: Debug Cart Information**

I've added a debug endpoint to help identify cart issues:

**Endpoint:** `GET /api/cart/debug/`
**Headers:** `Authorization: Bearer YOUR_JWT_TOKEN`

This will show:
- User's cart information
- Active/inactive carts
- Cart items
- Total amounts

### **Step 2: Fix Frontend Cart Management**

Update your Next.js frontend to properly handle cart states:

```javascript
// utils/cartUtils.js
export const getOrCreateCart = async () => {
  try {
    // First, try to get existing cart
    const response = await fetch('/api/cart/', {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      }
    });
    
    const cartData = await response.json();
    
    // If cart exists and has items, return it
    if (cartData.id && cartData.items && cartData.items.length > 0) {
      return cartData;
    }
    
    // If no cart or empty cart, create one by adding a service
    // (Cart is created automatically when first item is added)
    return null;
    
  } catch (error) {
    console.error('Error getting cart:', error);
    return null;
  }
};

export const addToCart = async (serviceId, quantity = 1) => {
  try {
    const response = await fetch('/api/cart/add/', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        service_id: serviceId,
        quantity: quantity
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      return result.cart;
    } else {
      throw new Error(result.error || 'Failed to add to cart');
    }
  } catch (error) {
    console.error('Error adding to cart:', error);
    throw error;
  }
};

export const createOrder = async (orderData) => {
  try {
    // Get current cart first
    const cart = await getOrCreateCart();
    
    if (!cart || !cart.id) {
      throw new Error('No active cart found. Please add items to cart first.');
    }
    
    if (!cart.items || cart.items.length === 0) {
      throw new Error('Cart is empty. Please add items to cart first.');
    }
    
    // Ensure all required fields are present
    const completeOrderData = {
      cart_id: cart.id.toString(), // Ensure cart_id is string
      delivery_address: {
        house_number: orderData.delivery_address.house_number || '',
        street_name: orderData.delivery_address.street_name || '',
        city: orderData.delivery_address.city || '',
        state: orderData.delivery_address.state || '',
        pincode: orderData.delivery_address.pincode || '',
        ...orderData.delivery_address
      },
      payment_method: orderData.payment_method || 'razorpay',
      scheduled_date: orderData.scheduled_date,
      scheduled_time_slot: orderData.scheduled_time_slot,
      customer_notes: orderData.customer_notes || ''
    };
    
    const response = await fetch('/api/orders/', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(completeOrderData)
    });
    
    const result = await response.json();
    
    if (response.ok && result.success) {
      return result;
    } else {
      throw new Error(result.message || 'Failed to create order');
    }
    
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};
```

### **Step 3: Fix Order Creation Flow**

Update your checkout component:

```javascript
// components/Checkout.js
import { useState, useEffect } from 'react';
import { getOrCreateCart, createOrder } from '../utils/cartUtils';

const Checkout = () => {
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(true);
  const [orderData, setOrderData] = useState({
    delivery_address: {
      house_number: '',
      street_name: '',
      city: '',
      state: '',
      pincode: ''
    },
    payment_method: 'razorpay',
    scheduled_date: '',
    scheduled_time_slot: '',
    customer_notes: ''
  });

  useEffect(() => {
    loadCart();
  }, []);

  const loadCart = async () => {
    try {
      const cartData = await getOrCreateCart();
      setCart(cartData);
    } catch (error) {
      console.error('Error loading cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrder = async () => {
    try {
      setLoading(true);
      
      // Validate required fields
      if (!orderData.delivery_address.house_number) {
        alert('Please enter house number');
        return;
      }
      
      if (!cart || !cart.id) {
        alert('No items in cart. Please add items first.');
        return;
      }
      
      const result = await createOrder(orderData);
      
      if (result.success) {
        // Order created successfully
        alert('Order created successfully!');
        // Redirect to order confirmation or payment
      }
      
    } catch (error) {
      console.error('Order creation error:', error);
      alert(error.message || 'Failed to create order');
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading...</div>;
  
  if (!cart || !cart.items || cart.items.length === 0) {
    return <div>Your cart is empty. Please add items first.</div>;
  }

  return (
    <div>
      {/* Cart items display */}
      <div>
        <h3>Cart Items ({cart.items.length})</h3>
        {cart.items.map(item => (
          <div key={item.id}>
            {item.service_title} - Qty: {item.quantity} - ₹{item.price_at_add}
          </div>
        ))}
        <div>Total: ₹{cart.total_amount}</div>
      </div>

      {/* Delivery address form */}
      <div>
        <h3>Delivery Address</h3>
        <input
          type="text"
          placeholder="House Number *"
          value={orderData.delivery_address.house_number}
          onChange={(e) => setOrderData({
            ...orderData,
            delivery_address: {
              ...orderData.delivery_address,
              house_number: e.target.value
            }
          })}
          required
        />
        {/* Add other address fields */}
      </div>

      {/* Payment method selection */}
      <div>
        <h3>Payment Method</h3>
        <label>
          <input
            type="radio"
            value="razorpay"
            checked={orderData.payment_method === 'razorpay'}
            onChange={(e) => setOrderData({...orderData, payment_method: e.target.value})}
          />
          Online Payment
        </label>
        <label>
          <input
            type="radio"
            value="cod"
            checked={orderData.payment_method === 'cod'}
            onChange={(e) => setOrderData({...orderData, payment_method: e.target.value})}
          />
          Cash on Delivery
        </label>
      </div>

      {/* Order button */}
      <button 
        onClick={handleCreateOrder}
        disabled={loading || !orderData.delivery_address.house_number}
      >
        {loading ? 'Creating Order...' : 'Place Order'}
      </button>
    </div>
  );
};

export default Checkout;
```

### **Step 4: Debug and Test**

1. **Check Cart Debug Info:**
   ```bash
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        http://localhost:8000/api/cart/debug/
   ```

2. **Test Cart Creation:**
   ```bash
   curl -X POST \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"service_id": 1, "quantity": 1}' \
        http://localhost:8000/api/cart/add/
   ```

3. **Test Order Creation:**
   ```bash
   curl -X POST \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cart_id": "YOUR_CART_ID",
          "delivery_address": {
            "house_number": "123",
            "street_name": "Main St",
            "city": "City",
            "state": "State",
            "pincode": "123456"
          },
          "payment_method": "cod"
        }' \
        http://localhost:8000/api/orders/
   ```

## 🎯 **Quick Fixes**

### **1. Ensure Django Server is Running:**
```bash
cd django_backend
python manage.py runserver
```

### **2. Check Server Status:**
Visit: http://127.0.0.1:8000/admin/

### **3. Test API Endpoints:**
- Cart: http://127.0.0.1:8000/api/cart/
- Debug: http://127.0.0.1:8000/api/cart/debug/
- Orders: http://127.0.0.1:8000/api/orders/

### **4. Frontend Environment Variables:**
Ensure your Next.js app has correct API base URL:
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
```

## 🚀 **Expected Results**

After implementing these fixes:

1. ✅ **Cart Creation:** Users can add items to cart
2. ✅ **Cart Persistence:** Cart data persists across sessions
3. ✅ **Order Creation:** Orders can be created from cart
4. ✅ **Payment Methods:** Both online and COD work
5. ✅ **Error Handling:** Proper error messages displayed

## 🔧 **Additional Debugging**

If issues persist:

1. **Check Django Logs:** Look for detailed error messages
2. **Check Browser Console:** Look for JavaScript errors
3. **Check Network Tab:** Verify API requests and responses
4. **Use Debug Endpoint:** Get detailed cart information
5. **Test with Postman:** Verify API endpoints work independently

## 📞 **Support**

The debug endpoint `/api/cart/debug/` will provide detailed information about:
- User's cart status
- Active/inactive carts
- Cart items and quantities
- Total amounts
- Creation/update timestamps

Use this information to identify exactly what's causing the "Cart not found" error.

**Your Django backend is working correctly - the issue is likely in the frontend cart management or API request formatting.**
