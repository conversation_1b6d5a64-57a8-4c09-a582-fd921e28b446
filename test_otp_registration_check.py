#!/usr/bin/env python
"""
Test script to demonstrate the new OTP registration check functionality.
This script tests both scenarios:
1. Sending OTP to a non-registered mobile number (should fail)
2. Sending OTP to a registered mobile number (should succeed)
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000"
HEADERS = {"Content-Type": "application/json"}

# Test mobile numbers
UNREGISTERED_MOBILE = "+919999999999"  # This should not be registered
REGISTERED_MOBILE = "+918328315313"    # This might be registered from previous tests

def test_send_otp_unregistered():
    """Test sending OTP to an unregistered mobile number"""
    print("🧪 Testing OTP send to UNREGISTERED mobile number...")
    
    url = f"{BASE_URL}/api/auth/otp/send/"
    data = {"mobile_number": UNREGISTERED_MOBILE}
    
    try:
        response = requests.post(url, headers=HEADERS, json=data)
        print(f"📱 Mobile: {UNREGISTERED_MOBILE}")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.json()}")
        
        if response.status_code == 400:
            response_data = response.json()
            if "No account found with this mobile number" in response_data.get("error", ""):
                print("✅ PASS: Correctly rejected unregistered mobile number")
                return True
            else:
                print("❌ FAIL: Wrong error message")
                return False
        else:
            print("❌ FAIL: Should have returned 400 status code")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: Request failed - {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ ERROR: Invalid JSON response - {e}")
        return False

def test_register_user():
    """Register a test user for the second test"""
    print("\n🧪 Registering a test user...")
    
    url = f"{BASE_URL}/api/auth/register/mobile/"
    data = {
        "mobile_number": REGISTERED_MOBILE,
        "name": "Test User",
        "user_type": "CUSTOMER"
    }
    
    try:
        response = requests.post(url, headers=HEADERS, json=data)
        print(f"📱 Mobile: {REGISTERED_MOBILE}")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.json()}")
        
        if response.status_code in [201, 400]:  # 201 for new user, 400 if already exists
            print("✅ User registration handled (new or existing)")
            return True
        else:
            print("❌ FAIL: Unexpected status code")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: Request failed - {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ ERROR: Invalid JSON response - {e}")
        return False

def test_send_otp_registered():
    """Test sending OTP to a registered mobile number"""
    print("\n🧪 Testing OTP send to REGISTERED mobile number...")
    
    url = f"{BASE_URL}/api/auth/otp/send/"
    data = {"mobile_number": REGISTERED_MOBILE}
    
    try:
        response = requests.post(url, headers=HEADERS, json=data)
        print(f"📱 Mobile: {REGISTERED_MOBILE}")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.json()}")
        
        if response.status_code == 200:
            response_data = response.json()
            if "OTP sent successfully" in response_data.get("message", ""):
                print("✅ PASS: Successfully sent OTP to registered mobile number")
                return True
            else:
                print("❌ FAIL: Wrong success message")
                return False
        else:
            print("❌ FAIL: Should have returned 200 status code")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: Request failed - {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ ERROR: Invalid JSON response - {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting OTP Registration Check Tests")
    print("=" * 50)
    
    # Test 1: Unregistered mobile number
    test1_passed = test_send_otp_unregistered()
    
    # Test 2: Register a user (if needed)
    test_register_user()
    
    # Test 3: Registered mobile number
    test2_passed = test_send_otp_registered()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    print(f"Test 1 (Unregistered Mobile): {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"Test 2 (Registered Mobile): {'✅ PASS' if test2_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! The OTP registration check is working correctly.")
    else:
        print("\n⚠️  SOME TESTS FAILED. Please check the implementation.")
    
    print("\n💡 Note: Make sure the Django server is running on http://127.0.0.1:8000")

if __name__ == "__main__":
    main()
