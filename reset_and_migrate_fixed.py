#!/usr/bin/env python
"""
Reset custom databases and migrate with the improved database router.
"""
import os
import django
from django.core.management import execute_from_command_line
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def reset_and_migrate_fixed():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')

    print("🔄 Resetting custom databases and migrating with improved router...")

    # Database connection details (update these with your actual credentials)
    db_config = {
        'host': '**************',
        'port': '5432',
        'user': 'vinupp',
        'password': 'Dhenu@1058'
    }

    # Custom databases to reset (keeping default database intact)
    custom_databases_map = {
        'catalogue': 'home_services_catalogue',
        'cart': 'home_services_cart',
        'coupons': 'home_services_coupons',
        'orders': 'home_services_orders',
        'payments': 'home_services_payments',
        'providers': 'home_services_providers'
    }

    print("\n🗑️ Resetting custom databases...")
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database='postgres'   # Connect to default postgres database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()

        for app, db_name in custom_databases_map.items():
            try:
                print(f"    Dropping {db_name}...")
                cursor.execute(f'DROP DATABASE IF EXISTS "{db_name}"')
                print(f"    Creating {db_name}...")
                cursor.execute(f'CREATE DATABASE "{db_name}"')
                print(f"    ✅ {db_name} reset successfully")
            except Exception as e:
                print(f"    ⚠️ {db_name} reset error: {e}")

        cursor.close()
        conn.close()
        print("✅ Database reset completed")

    except Exception as e:
        print(f"❌ Database reset failed: {e}")
        print("Please reset databases manually and run migration script")
        return

    # Initialize Django after database reset
    django.setup()

    print("\n📊 Running migrations with improved router...")

    # Step 1: Migrate core Django apps to default database only
    print("\n🔧 Step 1: Migrating core apps to default database...")
    core_apps = ['contenttypes', 'auth', 'admin', 'sessions', 'authentication']

    for app in core_apps:
        try:
            print(f"    Migrating {app} to default...")
            execute_from_command_line([
                'manage.py', 'migrate', app,
                '--database=default',
                '--skip-checks',
                '--verbosity=1'
            ])
            print(f"    ✅ {app} migrated to default")
        except Exception as e:
            print(f"    ⚠️ {app} error: {e}")

    # Step 2: Create new migrations with db_constraint=False
    # (This step is technically less crucial here as we already did this,
    # but good for ensuring fresh migrations if models change later)
    print("\n🔧 Step 2: Creating new migrations with cross-database support...")
    apps_to_makemigrations = custom_databases_map.keys()

    for app in apps_to_makemigrations:
        try:
            print(f"    Creating migrations for {app}...")
            execute_from_command_line([
                'manage.py', 'makemigrations', app,
                '--skip-checks',
                '--verbosity=1'
            ])
            print(f"    ✅ {app} migrations created")
        except Exception as e:
            print(f"    ⚠️ {app} migration creation error: {e}")

    # Step 3: Run migrations for each custom app to its specific database
    print("\n🔧 Step 3: Running migrations for each custom app to its dedicated database...")
    for app, db_name in custom_databases_map.items():
        db_alias = [k for k, v in django.conf.settings.DATABASES.items() if v.get('NAME') == db_name][0]
        try:
            print(f"    Applying {app} migrations to {db_alias} ({db_name})...")
            execute_from_command_line([
                'manage.py', 'migrate', app,
                '--database=' + db_alias, # Use the database alias here
                '--skip-checks',
                '--verbosity=3' # High verbosity to see details
            ])
            print(f"    ✅ {app} migrations applied to {db_alias}")
        except Exception as e:
            print(f"    ❌ Failed to apply {app} migrations to {db_alias}: {e}")

    print("\n🎉 Migration completed with improved router!")
    print("\n📋 Database Structure:")
    print("    🗄️ home_services_auth: authentication + core Django apps")
    print("    🗄️ home_services_catalogue: catalogue app only")
    print("    🗄️ home_services_cart: cart app only")
    print("    🗄️ home_services_coupons: coupons app only")
    print("    🗄️ home_services_orders: orders app only")
    print("    🗄️ home_services_payments: payments app only")
    print("    🗄️ home_services_providers: providers app only")

    print("\n📋 Next steps:")
    print("1. Run: python verify_databases.py")
    print("2. Run: python manage.py createsuperuser")
    print("3. Run: python manage.py runserver")

if __name__ == '__main__':
    reset_and_migrate_fixed()