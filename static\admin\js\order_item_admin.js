/**
 * Enhanced Order Item Admin JavaScript
 * Handles service selection and auto-population of service details
 */

(function($) {
    'use strict';

    // Service data cache
    let serviceData = {};

    // Initialize when DOM is ready
    $(document).ready(function() {
        initializeServiceSelection();
        loadServiceData();
    });

    function initializeServiceSelection() {
        // Handle service selection changes
        $(document).on('change', 'select[name*="service_selection"]', function() {
            const serviceId = $(this).val();
            const row = $(this).closest('tr');
            
            if (serviceId && serviceData[serviceId]) {
                populateServiceDetails(row, serviceData[serviceId]);
            }
        });

        // Handle dynamic inline addition
        $(document).on('formset:added', function(event, $row) {
            initializeNewRow($row);
        });
    }

    function loadServiceData() {
        // Load service data from catalogue database via AJAX
        $.ajax({
            url: '/api/orders/admin/get-services/',
            method: 'GET',
            success: function(data) {
                serviceData = data;
            },
            error: function() {
                console.log('Could not load service data');
            }
        });
    }

    function populateServiceDetails(row, service) {
        // Auto-populate service details when a service is selected
        const serviceIdField = row.find('input[name*="service_id"]');
        const serviceTitleField = row.find('input[name*="service_title"]');
        const unitPriceField = row.find('input[name*="unit_price"]');
        const discountField = row.find('input[name*="discount_per_unit"]');
        const durationField = row.find('input[name*="estimated_duration"]');

        // Populate fields
        serviceIdField.val(service.id);
        serviceTitleField.val(service.title);
        
        // Use discount price if available, otherwise base price
        const currentPrice = service.discount_price || service.base_price;
        unitPriceField.val(currentPrice);
        
        // Calculate discount per unit if there's a discount price
        if (service.discount_price && service.base_price) {
            const discount = parseFloat(service.base_price) - parseFloat(service.discount_price);
            discountField.val(discount.toFixed(2));
        }
        
        // Set estimated duration if available
        if (service.time_to_complete) {
            durationField.val(service.time_to_complete);
        }

        // Trigger calculation of total price
        calculateTotalPrice(row);
    }

    function calculateTotalPrice(row) {
        // Calculate total price based on quantity, unit price, and discount
        const quantityField = row.find('input[name*="quantity"]');
        const unitPriceField = row.find('input[name*="unit_price"]');
        const discountField = row.find('input[name*="discount_per_unit"]');
        const totalPriceField = row.find('input[name*="total_price"]');

        const quantity = parseFloat(quantityField.val()) || 1;
        const unitPrice = parseFloat(unitPriceField.val()) || 0;
        const discount = parseFloat(discountField.val()) || 0;

        const totalPrice = (unitPrice - discount) * quantity;
        totalPriceField.val(totalPrice.toFixed(2));

        // Update order totals
        updateOrderTotals();
    }

    function updateOrderTotals() {
        // Calculate and update order subtotal and total
        let subtotal = 0;
        
        $('input[name*="total_price"]').each(function() {
            const value = parseFloat($(this).val()) || 0;
            subtotal += value;
        });

        // Update subtotal field
        $('#id_subtotal').val(subtotal.toFixed(2));

        // Trigger tax calculation if auto-calculate is enabled
        if ($('#id_auto_calculate_tax').is(':checked')) {
            calculateTaxes(subtotal);
        }
    }

    function calculateTaxes(subtotal) {
        // Calculate taxes via AJAX call to taxation service
        const discountAmount = parseFloat($('#id_discount_amount').val()) || 0;
        const taxableAmount = subtotal - discountAmount;

        $.ajax({
            url: '/api/orders/admin/calculate-tax/',
            method: 'POST',
            data: {
                'amount': taxableAmount,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(data) {
                $('#id_tax_amount').val(data.total_tax);
                
                // Update individual tax components if fields exist
                if ($('#id_cgst_amount').length) {
                    $('#id_cgst_amount').val(data.cgst || '0.00');
                    $('#id_sgst_amount').val(data.sgst || '0.00');
                    $('#id_igst_amount').val(data.igst || '0.00');
                }

                // Update total amount
                updateTotalAmount();
            },
            error: function() {
                console.log('Could not calculate taxes');
            }
        });
    }

    function updateTotalAmount() {
        // Calculate final total amount
        const subtotal = parseFloat($('#id_subtotal').val()) || 0;
        const taxAmount = parseFloat($('#id_tax_amount').val()) || 0;
        const discountAmount = parseFloat($('#id_discount_amount').val()) || 0;
        const minimumOrderFee = parseFloat($('#id_minimum_order_fee').val()) || 0;

        const totalAmount = subtotal + taxAmount - discountAmount + minimumOrderFee;
        $('#id_total_amount').val(totalAmount.toFixed(2));
    }

    function initializeNewRow($row) {
        // Initialize newly added inline rows
        const serviceSelect = $row.find('select[name*="service_selection"]');
        if (serviceSelect.length) {
            // Populate service options for new row
            populateServiceOptions(serviceSelect);
        }
    }

    function populateServiceOptions(selectElement) {
        // Populate service options in select element
        selectElement.empty();
        selectElement.append('<option value="">--- Select Service ---</option>');
        
        for (const serviceId in serviceData) {
            const service = serviceData[serviceId];
            const currentPrice = service.discount_price || service.base_price;
            const optionText = `${service.category_name} - ${service.title} (₹${currentPrice})`;
            selectElement.append(`<option value="${serviceId}">${optionText}</option>`);
        }
    }

    // Bind events for real-time calculations
    $(document).on('input', 'input[name*="quantity"], input[name*="unit_price"], input[name*="discount_per_unit"]', function() {
        const row = $(this).closest('tr');
        calculateTotalPrice(row);
    });

    // Handle coupon application
    $(document).on('blur', '#id_apply_coupon', function() {
        const couponCode = $(this).val();
        if (couponCode) {
            applyCoupon(couponCode);
        }
    });

    function applyCoupon(couponCode) {
        const subtotal = parseFloat($('#id_subtotal').val()) || 0;
        
        $.ajax({
            url: '/api/orders/admin/apply-coupon/',
            method: 'POST',
            data: {
                'coupon_code': couponCode,
                'amount': subtotal,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(data) {
                if (data.success) {
                    $('#id_coupon_code').val(couponCode.toUpperCase());
                    $('#id_coupon_discount').val(data.discount_amount);
                    $('#id_discount_amount').val(data.discount_amount);
                    
                    // Show success message
                    showMessage('Coupon applied successfully!', 'success');
                    
                    // Recalculate totals
                    updateOrderTotals();
                } else {
                    showMessage(data.message || 'Invalid coupon code', 'error');
                }
            },
            error: function() {
                showMessage('Error applying coupon', 'error');
            }
        });
    }

    function showMessage(message, type) {
        // Show admin message
        const messageClass = type === 'success' ? 'success' : 'error';
        const messageHtml = `<div class="alert alert-${messageClass}">${message}</div>`;
        
        // Remove existing messages
        $('.alert').remove();
        
        // Add new message
        $('.form-row').first().before(messageHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

})(django.jQuery);
