# CORS Configuration Fix - Complete Guide

## Problem Summary
Your Next.js frontend (localhost:3000) was unable to access your Django backend (localhost:8000) due to CORS (Cross-Origin Resource Sharing) restrictions.

## What Was Fixed

### 1. Enhanced CORS Settings in Django
Updated `home_services/settings.py` with comprehensive CORS configuration:

```python
# CORS Configuration
CORS_ALLOWED_ORIGINS = config('CORS_ALLOWED_ORIGINS', default='http://localhost:3000').split(',')
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = DEBUG  # Only in development

# Additional CORS settings for better compatibility
CORS_ALLOWED_ORIGIN_REGEXES = [
    r"^http://localhost:\d+$",  # Allow any localhost port
    r"^http://127\.0\.0\.1:\d+$",  # Allow any 127.0.0.1 port
]

# Allow specific headers that your frontend might send
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# Allow specific methods
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# Preflight request max age
CORS_PREFLIGHT_MAX_AGE = 86400
```

### 2. Updated Environment Variables
Enhanced `.env` file with additional allowed origins:

```env
# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:3001,https://your-production-domain.com
```

### 3. Verified Middleware Order
Confirmed that `corsheaders.middleware.CorsMiddleware` is at the top of the middleware stack in settings.py.

### 4. Confirmed API Permissions
Verified that catalogue endpoints have proper permissions:
- Categories: Public access (AllowAny) for GET requests
- Services: Public access (AllowAny) for GET requests
- Admin operations: Restricted to admin users

## Testing the Fix

### Method 1: Start Django Server
First, make sure your Django server is running:

**Windows:**
```cmd
run_server.bat
```

**Or manually:**
```cmd
venv\Scripts\activate.bat
python manage.py runserver
```

**Linux/Mac:**
```bash
./run_server.sh
# Or manually:
source venv/bin/activate
python manage.py runserver
```

### Method 2: Run the Test Script
Once the server is running, open a new terminal and run:
```bash
python test_cors.py
```

This script will test:
- CORS preflight requests (OPTIONS)
- API endpoint accessibility
- Response structure validation

### Method 2: Manual Testing with curl
```bash
# Test categories endpoint
curl -H "Origin: http://localhost:3000" \
     -H "Content-Type: application/json" \
     -X GET http://localhost:8000/api/catalogue/categories/

# Test preflight request
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS http://localhost:8000/api/catalogue/categories/
```

### Method 3: Browser Developer Tools
1. Open your Next.js app (localhost:3000)
2. Open browser developer tools (F12)
3. Check Network tab for API requests
4. Look for CORS errors in Console tab

## Expected Results

After applying these fixes, you should see:

1. **No CORS errors** in browser console
2. **Categories displaying** on your Next.js frontend
3. **API responses** with proper CORS headers:
   - `Access-Control-Allow-Origin: http://localhost:3000`
   - `Access-Control-Allow-Credentials: true`
   - `Access-Control-Allow-Methods: DELETE, GET, OPTIONS, PATCH, POST, PUT`

## Troubleshooting

### If CORS errors persist:

1. **Restart Django server**:
   ```bash
   python manage.py runserver
   ```

2. **Clear browser cache** and hard refresh (Ctrl+Shift+R)

3. **Check Django logs** for any errors:
   ```bash
   tail -f logs/django.log
   ```

4. **Verify environment variables** are loaded:
   ```bash
   python manage.py shell
   >>> from django.conf import settings
   >>> print(settings.CORS_ALLOWED_ORIGINS)
   ```

### If API returns empty results:

1. **Check database** has data:
   ```bash
   python manage.py shell
   >>> from catalogue.models import Category
   >>> print(Category.objects.filter(is_active=True).count())
   ```

2. **Run migrations** if needed:
   ```bash
   python manage.py migrate
   ```

3. **Create test data** if database is empty:
   ```bash
   python manage.py shell
   >>> from catalogue.models import Category
   >>> Category.objects.create(name="Test Category", slug="test-category")
   ```

## Production Considerations

For production deployment:

1. **Update CORS_ALLOWED_ORIGINS** in production environment:
   ```env
   CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
   ```

2. **Disable CORS_ALLOW_ALL_ORIGINS** in production:
   ```python
   CORS_ALLOW_ALL_ORIGINS = False  # Never True in production
   ```

3. **Enable HTTPS security settings**:
   ```env
   SECURE_SSL_REDIRECT=True
   SECURE_HSTS_SECONDS=31536000
   ```

## Next Steps

1. Test your Next.js frontend - categories should now display
2. Verify all API endpoints work correctly
3. Test with different browsers
4. Prepare for production deployment with proper domain configuration

## Files Modified

- `home_services/settings.py` - Enhanced CORS configuration
- `.env` - Updated CORS allowed origins
- `test_cors.py` - New test script (created)
- `CORS_FIX_COMPLETE.md` - This documentation (created)

## Quick Verification Steps

1. **Start Django Server:**
   ```cmd
   run_server.bat
   ```

2. **Open your browser and test these URLs directly:**
   - http://localhost:8000/api/catalogue/categories/
   - http://localhost:8000/api/catalogue/services/
   - http://localhost:8000/api/catalogue/categories/tree/

3. **Check your Next.js frontend:**
   - Open http://localhost:3000
   - Categories should now display without CORS errors
   - Check browser console (F12) - no CORS errors should appear

4. **Test with browser developer tools:**
   - Open Network tab in developer tools
   - Refresh your Next.js page
   - Look for successful API calls to localhost:8000

The CORS issue should now be completely resolved! 🎉
