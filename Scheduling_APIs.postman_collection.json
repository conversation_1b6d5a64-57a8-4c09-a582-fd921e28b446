{"info": {"name": "Home Services - Scheduling APIs", "description": "Complete API collection for Scheduling & Slot Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}, {"key": "auth_token", "value": "YOUR_JWT_TOKEN_HERE", "type": "string"}], "item": [{"name": "Slot Configuration", "item": [{"name": "List Configurations", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scheduling/configurations/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "configurations", ""]}}}, {"name": "Create Configuration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Standard Service Slots\",\n  \"slot_interval_minutes\": 60,\n  \"buffer_time_minutes\": 15,\n  \"advance_booking_days\": 30,\n  \"same_day_booking_cutoff_hours\": 2,\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/scheduling/configurations/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "configurations", ""]}}}, {"name": "Get Configuration Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scheduling/configurations/1/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "configurations", "1", ""]}}}]}, {"name": "Working Shifts", "item": [{"name": "List Working Shifts", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scheduling/working-shifts/?configuration=1", "host": ["{{base_url}}"], "path": ["api", "scheduling", "working-shifts", ""], "query": [{"key": "configuration", "value": "1"}]}}}, {"name": "Create Working Shift", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"configuration\": 1,\n  \"weekday\": 0,\n  \"start_time\": \"09:00:00\",\n  \"end_time\": \"18:00:00\",\n  \"is_working_day\": true,\n  \"max_bookings_per_slot\": 2\n}"}, "url": {"raw": "{{base_url}}/api/scheduling/working-shifts/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "working-shifts", ""]}}}]}, {"name": "Holidays", "item": [{"name": "List Holidays", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scheduling/holidays/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "holidays", ""]}}}, {"name": "Create Holiday", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Year's Day\",\n  \"date\": \"2024-01-01\",\n  \"holiday_type\": \"national\",\n  \"is_recurring\": true,\n  \"description\": \"National holiday - New Year celebration\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/scheduling/holidays/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "holidays", ""]}}}]}, {"name": "Time Slots", "item": [{"name": "List Time Slots", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scheduling/slots/?date=2024-01-15", "host": ["{{base_url}}"], "path": ["api", "scheduling", "slots", ""], "query": [{"key": "date", "value": "2024-01-15"}]}}}, {"name": "Get Available Slots", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scheduling/slots/available/?date=2024-01-15&service_duration_minutes=60", "host": ["{{base_url}}"], "path": ["api", "scheduling", "slots", "available", ""], "query": [{"key": "date", "value": "2024-01-15"}, {"key": "service_duration_minutes", "value": "60"}]}}}, {"name": "Generate Slots (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"configuration_id\": 1,\n  \"start_date\": \"2024-01-01\",\n  \"end_date\": \"2024-01-31\"\n}"}, "url": {"raw": "{{base_url}}/api/scheduling/slots/generate/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "slots", "generate", ""]}}}, {"name": "Bulk Update Slots (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"slot_ids\": [\"uuid1\", \"uuid2\", \"uuid3\"],\n  \"action\": \"block\",\n  \"reason\": \"Maintenance period\"\n}"}, "url": {"raw": "{{base_url}}/api/scheduling/slots/bulk-update/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "slots", "bulk-update", ""]}}}]}, {"name": "Bookings", "item": [{"name": "List Bookings", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scheduling/bookings/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "bookings", ""]}}}, {"name": "Create Booking", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"time_slot_id\": \"uuid-of-time-slot\",\n  \"order_id\": \"ORDER123\",\n  \"customer_mobile\": \"9876543210\",\n  \"customer_name\": \"<PERSON>\",\n  \"quantity\": 1,\n  \"service_names\": [\"Home Cleaning\", \"Kitchen Deep Clean\"],\n  \"customer_notes\": \"Please call before arriving\"\n}"}, "url": {"raw": "{{base_url}}/api/scheduling/bookings/create/", "host": ["{{base_url}}"], "path": ["api", "scheduling", "bookings", "create", ""]}}}, {"name": "Filter Bookings by Order", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scheduling/bookings/?order_id=ORDER123", "host": ["{{base_url}}"], "path": ["api", "scheduling", "bookings", ""], "query": [{"key": "order_id", "value": "ORDER123"}]}}}, {"name": "Filter Bookings by Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scheduling/bookings/?status=confirmed", "host": ["{{base_url}}"], "path": ["api", "scheduling", "bookings", ""], "query": [{"key": "status", "value": "confirmed"}]}}}]}]}