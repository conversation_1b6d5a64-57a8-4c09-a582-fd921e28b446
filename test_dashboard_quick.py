#!/usr/bin/env python3
"""
Quick test for dashboard API with customer names
"""
import requests
import json

def test_dashboard():
    # Login
    login_response = requests.post(
        "http://localhost:8000/api/auth/login/email/",
        json={"email": "<EMAIL>", "password": "admin123"}
    )
    
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.text}")
        return
    
    token = login_response.json()['tokens']['access']
    print("Login successful")
    
    # Test dashboard
    headers = {"Authorization": f"Bearer {token}"}
    dashboard_response = requests.get(
        "http://localhost:8000/api/orders/dashboard/",
        headers=headers
    )
    
    print(f"Dashboard status: {dashboard_response.status_code}")
    if dashboard_response.status_code == 200:
        data = dashboard_response.json()
        print(f"Total orders: {data['total_orders']}")
        print(f"Recent orders: {len(data['recent_orders'])}")
        
        # Check first order for customer name
        if data['recent_orders']:
            first_order = data['recent_orders'][0]
            print(f"First order: {first_order['order_number']}")
            print(f"Customer name: {first_order.get('customer_name', 'NOT FOUND')}")
            print(f"Customer mobile: {first_order.get('customer_mobile', 'NOT FOUND')}")
    else:
        print(f"Dashboard error: {dashboard_response.text}")

if __name__ == "__main__":
    test_dashboard()
