from django.core.management.base import BaseCommand
from django.db import connections
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'Clean up orphaned data across microservice databases'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--database',
            type=str,
            choices=['cart', 'orders', 'payments', 'all'],
            default='all',
            help='Which database to clean up (default: all)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        database = options['database']
        
        self.stdout.write(
            self.style.SUCCESS(
                f'🧹 Starting orphaned data cleanup (dry_run={dry_run})...'
            )
        )
        
        # Get all valid user IDs from the authentication database
        valid_user_ids = set(User.objects.values_list('id', flat=True))
        self.stdout.write(f'Found {len(valid_user_ids)} valid users in authentication database')
        
        if database in ['cart', 'all']:
            self.cleanup_cart_data(valid_user_ids, dry_run)
            
        if database in ['orders', 'all']:
            self.cleanup_order_data(valid_user_ids, dry_run)
            
        if database in ['payments', 'all']:
            self.cleanup_payment_data(valid_user_ids, dry_run)
        
        self.stdout.write(
            self.style.SUCCESS('✅ Cleanup completed!')
        )

    def cleanup_cart_data(self, valid_user_ids, dry_run):
        """Clean up orphaned cart data"""
        try:
            cart_db = connections['cart_db']
            with cart_db.cursor() as cursor:
                # Check if tables exist
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'cart_cart'
                    );
                """)
                
                if not cursor.fetchone()[0]:
                    self.stdout.write('⚠️ Cart tables do not exist, skipping...')
                    return
                
                # Find orphaned carts
                cursor.execute("""
                    SELECT id, user_id FROM cart_cart 
                    WHERE user_id IS NOT NULL
                """)
                
                orphaned_carts = []
                for cart_id, user_id in cursor.fetchall():
                    if user_id not in valid_user_ids:
                        orphaned_carts.append((cart_id, user_id))
                
                if orphaned_carts:
                    self.stdout.write(f'🛒 Found {len(orphaned_carts)} orphaned carts')
                    
                    if not dry_run:
                        # Delete orphaned cart items first
                        orphaned_cart_ids = [cart_id for cart_id, _ in orphaned_carts]
                        cursor.execute(
                            f"DELETE FROM cart_cartitem WHERE cart_id IN ({','.join(map(str, orphaned_cart_ids))})"
                        )
                        
                        # Delete orphaned carts
                        cursor.execute(
                            f"DELETE FROM cart_cart WHERE id IN ({','.join(map(str, orphaned_cart_ids))})"
                        )
                        
                        self.stdout.write(
                            self.style.SUCCESS(f'✅ Deleted {len(orphaned_carts)} orphaned carts')
                        )
                    else:
                        for cart_id, user_id in orphaned_carts:
                            self.stdout.write(f'  Would delete cart {cart_id} (user_id: {user_id})')
                else:
                    self.stdout.write('✅ No orphaned cart data found')
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error cleaning cart data: {str(e)}')
            )

    def cleanup_order_data(self, valid_user_ids, dry_run):
        """Clean up orphaned order data"""
        try:
            orders_db = connections['orders_db']
            with orders_db.cursor() as cursor:
                # Check if tables exist
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'orders_order'
                    );
                """)
                
                if not cursor.fetchone()[0]:
                    self.stdout.write('⚠️ Order tables do not exist, skipping...')
                    return
                
                # Find orphaned orders
                cursor.execute("""
                    SELECT id, customer_id, assigned_provider_id FROM orders_order
                """)
                
                orphaned_orders = []
                for order_id, customer_id, provider_id in cursor.fetchall():
                    if (customer_id and customer_id not in valid_user_ids) or \
                       (provider_id and provider_id not in valid_user_ids):
                        orphaned_orders.append(order_id)
                
                if orphaned_orders:
                    self.stdout.write(f'📦 Found {len(orphaned_orders)} orphaned orders')
                    
                    if not dry_run:
                        # Delete orphaned order items first
                        order_ids_str = "','".join(map(str, orphaned_orders))
                        cursor.execute(
                            f"DELETE FROM orders_orderitem WHERE order_id IN ('{order_ids_str}')"
                        )

                        # Delete orphaned orders
                        cursor.execute(
                            f"DELETE FROM orders_order WHERE id IN ('{order_ids_str}')"
                        )
                        
                        self.stdout.write(
                            self.style.SUCCESS(f'✅ Deleted {len(orphaned_orders)} orphaned orders')
                        )
                    else:
                        for order_id in orphaned_orders:
                            self.stdout.write(f'  Would delete order {order_id}')
                else:
                    self.stdout.write('✅ No orphaned order data found')
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error cleaning order data: {str(e)}')
            )

    def cleanup_payment_data(self, valid_user_ids, dry_run):
        """Clean up orphaned payment data"""
        try:
            payments_db = connections['payments_db']
            with payments_db.cursor() as cursor:
                # Check if tables exist
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'payments_paymenttransaction'
                    );
                """)
                
                if not cursor.fetchone()[0]:
                    self.stdout.write('⚠️ Payment tables do not exist, skipping...')
                    return
                
                # Find orphaned payments
                cursor.execute("""
                    SELECT id, user_id FROM payments_paymenttransaction
                """)
                
                orphaned_payments = []
                for payment_id, user_id in cursor.fetchall():
                    if user_id not in valid_user_ids:
                        orphaned_payments.append(payment_id)
                
                if orphaned_payments:
                    self.stdout.write(f'💳 Found {len(orphaned_payments)} orphaned payments')
                    
                    if not dry_run:
                        # Delete orphaned payments
                        payment_ids_str = "','".join(map(str, orphaned_payments))
                        cursor.execute(
                            f"DELETE FROM payments_paymenttransaction WHERE id IN ('{payment_ids_str}')"
                        )
                        
                        self.stdout.write(
                            self.style.SUCCESS(f'✅ Deleted {len(orphaned_payments)} orphaned payments')
                        )
                    else:
                        for payment_id in orphaned_payments:
                            self.stdout.write(f'  Would delete payment {payment_id}')
                else:
                    self.stdout.write('✅ No orphaned payment data found')
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error cleaning payment data: {str(e)}')
            )
