#!/usr/bin/env python3
"""
Test script to verify SEO fields implementation.
This script tests the new SEO fields in Category and Service models.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from catalogue.models import Category, Service
from catalogue.serializers import CategorySerializer, ServiceSerializer

def test_seo_fields():
    """Test SEO fields functionality"""
    print("🔍 Testing SEO Fields Implementation")
    print("=" * 50)
    
    # Test Category SEO fields
    print("\n📁 Testing Category SEO Fields:")
    categories = Category.objects.filter(is_active=True)[:2]
    
    if categories.exists():
        for category in categories:
            print(f"\n  Category: {category.name}")
            print(f"  SEO Title: {category.get_seo_title()}")
            print(f"  SEO Description: {category.get_seo_description()[:100]}...")
            print(f"  OG Image: {category.get_og_image()}")
            
            # Test serializer
            serializer = CategorySerializer(category)
            seo_fields = {
                'meta_title': serializer.data.get('meta_title'),
                'meta_description': serializer.data.get('meta_description'),
                'og_image_url': serializer.data.get('og_image_url'),
                'seo_title': serializer.data.get('seo_title'),
                'seo_description': serializer.data.get('seo_description'),
                'og_image': serializer.data.get('og_image'),
            }
            print(f"  Serializer SEO Fields: {seo_fields}")
    else:
        print("  ⚠️  No categories found. Create some categories first.")
    
    # Test Service SEO fields
    print("\n🛠️  Testing Service SEO Fields:")
    services = Service.objects.filter(is_active=True)[:2]
    
    if services.exists():
        for service in services:
            print(f"\n  Service: {service.title}")
            print(f"  SEO Title: {service.get_seo_title()}")
            print(f"  SEO Description: {service.get_seo_description()[:100]}...")
            print(f"  OG Image: {service.get_og_image()}")
            
            # Test serializer
            serializer = ServiceSerializer(service)
            seo_fields = {
                'meta_title': serializer.data.get('meta_title'),
                'meta_description': serializer.data.get('meta_description'),
                'og_image_url': serializer.data.get('og_image_url'),
                'seo_title': serializer.data.get('seo_title'),
                'seo_description': serializer.data.get('seo_description'),
                'og_image': serializer.data.get('og_image'),
            }
            print(f"  Serializer SEO Fields: {seo_fields}")
    else:
        print("  ⚠️  No services found. Create some services first.")
    
    print("\n" + "=" * 50)
    print("✅ SEO Fields Test Complete!")
    print("\n💡 Next Steps:")
    print("   1. Run migrations: create_seo_migration.bat")
    print("   2. Add SEO content via Django admin")
    print("   3. Test API endpoints for SEO fields")
    print("   4. Update Next.js frontend to use SEO fields")

def test_model_fields():
    """Test if SEO fields exist in models"""
    print("\n🔧 Testing Model Field Existence:")
    
    # Test Category model
    category_fields = [f.name for f in Category._meta.get_fields()]
    seo_fields = ['meta_title', 'meta_description', 'og_image_url']
    
    print(f"  Category fields: {len(category_fields)} total")
    for field in seo_fields:
        if field in category_fields:
            print(f"  ✅ {field} - Found")
        else:
            print(f"  ❌ {field} - Missing (run migrations)")
    
    # Test Service model
    service_fields = [f.name for f in Service._meta.get_fields()]
    print(f"  Service fields: {len(service_fields)} total")
    for field in seo_fields:
        if field in service_fields:
            print(f"  ✅ {field} - Found")
        else:
            print(f"  ❌ {field} - Missing (run migrations)")

if __name__ == "__main__":
    try:
        test_model_fields()
        test_seo_fields()
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("Make sure Django is properly configured and migrations are run.")
        sys.exit(1)
