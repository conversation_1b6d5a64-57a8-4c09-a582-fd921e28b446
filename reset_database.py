#!/usr/bin/env python
"""
Database reset script for Home Services Authentication Service
This script will clean up the database and apply fresh migrations
"""
import os
import sys
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from django.conf import settings
from django.core.management import execute_from_command_line


def reset_database():
    """Reset the database by dropping and recreating it"""
    db_settings = settings.DATABASES['default']
    
    print("🔄 Resetting database...")
    
    try:
        # Connect to PostgreSQL server (not to the specific database)
        conn = psycopg2.connect(
            host=db_settings['HOST'],
            port=db_settings['PORT'],
            user=db_settings['USER'],
            password=db_settings['PASSWORD'],
            database='postgres'  # Connect to default postgres database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Drop the database if it exists
        print(f"🗑️  Dropping database '{db_settings['NAME']}'...")
        cursor.execute(f"DROP DATABASE IF EXISTS {db_settings['NAME']}")
        
        # Create the database
        print(f"🆕 Creating database '{db_settings['NAME']}'...")
        cursor.execute(f"CREATE DATABASE {db_settings['NAME']}")
        
        cursor.close()
        conn.close()
        
        print("✅ Database reset successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error resetting database: {e}")
        return False


def run_migrations():
    """Run fresh migrations"""
    print("🔄 Running migrations...")
    
    try:
        # Make migrations
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        # Apply migrations
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("✅ Migrations completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error running migrations: {e}")
        return False


def create_superuser():
    """Create a superuser"""
    print("👤 Creating superuser...")
    
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        if not User.objects.filter(email='<EMAIL>').exists():
            User.objects.create_superuser(
                email='<EMAIL>',
                name='Admin User',
                password='admin123'
            )
            print("✅ Superuser created successfully!")
            print("   Email: <EMAIL>")
            print("   Password: admin123")
        else:
            print("ℹ️  Superuser already exists")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating superuser: {e}")
        return False


def main():
    """Main reset function"""
    print("🚀 Home Services Authentication Service - Database Reset")
    print("=" * 60)
    
    # Ask for confirmation
    confirm = input("⚠️  This will DELETE ALL DATA in the database. Continue? (y/N): ").lower().strip()
    
    if confirm not in ['y', 'yes']:
        print("❌ Operation cancelled.")
        sys.exit(0)
    
    # Reset database
    if not reset_database():
        print("❌ Database reset failed.")
        sys.exit(1)
    
    # Run migrations
    if not run_migrations():
        print("❌ Migration failed.")
        sys.exit(1)
    
    # Create superuser
    if not create_superuser():
        print("❌ Superuser creation failed.")
        sys.exit(1)
    
    print("\n✅ Database reset completed successfully!")
    print("\nNext steps:")
    print("1. Start Redis server")
    print("2. Run: python manage.py runserver")
    print("3. Visit: http://localhost:8000/admin/")
    print("4. Visit: http://localhost:8000/api/docs/")


if __name__ == '__main__':
    main()
