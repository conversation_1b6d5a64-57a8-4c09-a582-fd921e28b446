from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import Address, FailedLoginAttempt
from .validators import (
    validate_mobile_number, validate_otp, validate_name,
    validate_user_type, validate_address_type, validate_zip_code
)
from .utils import validate_mobile_number as normalize_mobile

User = get_user_model()


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'email', 'mobile_number', 'name', 'user_type',
            'password', 'confirm_password'
        ]
        extra_kwargs = {
            'email': {'required': False},
            'mobile_number': {'required': False},
        }
    
    def validate(self, attrs):
        # Check password confirmation
        if attrs.get('password') != attrs.get('confirm_password'):
            raise serializers.ValidationError("Passwords don't match")
        
        # Validate based on user type
        user_type = attrs.get('user_type', 'CUSTOMER')
        
        if user_type == 'STAFF':
            if not attrs.get('email'):
                raise serializers.ValidationError("Email is required for staff users")
        elif user_type in ['CUSTOMER', 'PROVIDER']:
            if not attrs.get('mobile_number'):
                raise serializers.ValidationError("Mobile number is required for customer/provider users")
            
            # Normalize mobile number
            mobile = normalize_mobile(attrs['mobile_number'])
            if not mobile:
                raise serializers.ValidationError("Invalid mobile number format")
            attrs['mobile_number'] = mobile
        
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('confirm_password')
        password = validated_data.pop('password')
        
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        return user


class MobileRegistrationSerializer(serializers.Serializer):
    """
    Serializer for mobile-based registration (Customer/Provider)
    """
    mobile_number = serializers.CharField(validators=[validate_mobile_number])
    name = serializers.CharField(validators=[validate_name])
    user_type = serializers.ChoiceField(
        choices=['CUSTOMER', 'PROVIDER'],
        default='CUSTOMER'
    )
    
    def validate_mobile_number(self, value):
        # Normalize mobile number
        mobile = normalize_mobile(value)
        if not mobile:
            raise serializers.ValidationError("Invalid mobile number format")
        
        # Check if mobile number already exists
        if User.objects.filter(mobile_number=mobile).exists():
            raise serializers.ValidationError("User with this mobile number already exists")
        
        return mobile
    
    def create(self, validated_data):
        return User.objects.create_user(**validated_data)


class EmailLoginSerializer(serializers.Serializer):
    """
    Serializer for email-based login (Staff)
    """
    email = serializers.EmailField()
    password = serializers.CharField()


class MobileLoginSerializer(serializers.Serializer):
    """
    Serializer for mobile-based login (Customer/Provider)
    """
    mobile_number = serializers.CharField(validators=[validate_mobile_number])
    otp = serializers.CharField(validators=[validate_otp])
    
    def validate_mobile_number(self, value):
        mobile = normalize_mobile(value)
        if not mobile:
            raise serializers.ValidationError("Invalid mobile number format")
        return mobile


class SendOTPSerializer(serializers.Serializer):
    """
    Serializer for sending OTP
    """
    mobile_number = serializers.CharField(validators=[validate_mobile_number])
    
    def validate_mobile_number(self, value):
        mobile = normalize_mobile(value)
        if not mobile:
            raise serializers.ValidationError("Invalid mobile number format")
        return mobile


class VerifyOTPSerializer(serializers.Serializer):
    """
    Serializer for OTP verification
    """
    mobile_number = serializers.CharField(validators=[validate_mobile_number])
    otp = serializers.CharField(validators=[validate_otp])
    
    def validate_mobile_number(self, value):
        mobile = normalize_mobile(value)
        if not mobile:
            raise serializers.ValidationError("Invalid mobile number format")
        return mobile


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profile
    """
    class Meta:
        model = User
        fields = [
            'id', 'email', 'mobile_number', 'name', 'user_type',
            'is_verified', 'profile_picture', 'date_joined'
        ]
        read_only_fields = ['id', 'user_type', 'date_joined']


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user profile
    """
    class Meta:
        model = User
        fields = ['name', 'profile_picture']


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer for password reset request
    """
    email = serializers.EmailField()
    
    def validate_email(self, value):
        if not User.objects.filter(email=value, user_type='STAFF').exists():
            raise serializers.ValidationError("No staff user found with this email address")
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Serializer for password reset confirmation
    """
    token = serializers.CharField()
    password = serializers.CharField(validators=[validate_password])
    confirm_password = serializers.CharField()
    
    def validate(self, attrs):
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs


class AddressSerializer(serializers.ModelSerializer):
    """
    Serializer for user addresses
    """
    class Meta:
        model = Address
        fields = [
            'id', 'address_type', 'street', 'city', 'state',
            'zip_code', 'landmark', 'is_default', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def validate_zip_code(self, value):
        validate_zip_code(value)
        return value


class FailedLoginAttemptSerializer(serializers.ModelSerializer):
    """
    Serializer for failed login attempts (Admin use)
    """
    user_name = serializers.CharField(source='user.name', read_only=True)
    
    class Meta:
        model = FailedLoginAttempt
        fields = [
            'id', 'user', 'user_name', 'mobile_number', 'email',
            'ip_address', 'timestamp'
        ]
        read_only_fields = ['id', 'timestamp']


class TokenRefreshSerializer(serializers.Serializer):
    """
    Serializer for token refresh
    """
    refresh = serializers.CharField()


class ChangePasswordSerializer(serializers.Serializer):
    """
    Serializer for changing password
    """
    old_password = serializers.CharField()
    new_password = serializers.CharField(validators=[validate_password])
    confirm_password = serializers.CharField()
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError("New passwords don't match")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value
