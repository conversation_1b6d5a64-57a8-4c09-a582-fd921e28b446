"""
Custom error handlers for the Home Services Authentication API
"""
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import logging

logger = logging.getLogger(__name__)


@csrf_exempt
def handler400(request, exception=None):
    """Handle 400 Bad Request errors"""
    logger.warning(f"400 Bad Request: {request.path} - {exception}")
    return JsonResponse({
        'error': 'Bad Request',
        'message': 'The request could not be understood by the server.',
        'status_code': 400
    }, status=400)


@csrf_exempt
def handler403(request, exception=None):
    """Handle 403 Forbidden errors"""
    logger.warning(f"403 Forbidden: {request.path} - {exception}")
    return JsonResponse({
        'error': 'Forbidden',
        'message': 'You do not have permission to access this resource.',
        'status_code': 403
    }, status=403)


@csrf_exempt
def handler404(request, exception=None):
    """Handle 404 Not Found errors"""
    logger.warning(f"404 Not Found: {request.path}")
    return JsonResponse({
        'error': 'Not Found',
        'message': 'The requested resource was not found.',
        'status_code': 404
    }, status=404)


@csrf_exempt
def handler500(request):
    """Handle 500 Internal Server Error"""
    logger.error(f"500 Internal Server Error: {request.path}")
    return JsonResponse({
        'error': 'Internal Server Error',
        'message': 'An unexpected error occurred. Please try again later.',
        'status_code': 500
    }, status=500)
