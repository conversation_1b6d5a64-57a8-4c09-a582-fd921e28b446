@echo off
echo ========================================
echo Home Services Authentication Service
echo Starting Development Server
echo ========================================

REM Check if virtual environment exists
if not exist "venv" (
    echo Virtual environment not found!
    echo Please run setup.bat first.
    pause
    exit /b 1
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo Checking Django configuration...
python manage.py check

if errorlevel 1 (
    echo Django configuration check failed!
    pause
    exit /b 1
)

echo.
echo Starting development server...
echo Server will be available at: http://localhost:8000
echo API Documentation: http://localhost:8000/api/docs/
echo Admin Interface: http://localhost:8000/admin/
echo.
echo Press Ctrl+C to stop the server
echo.

python manage.py runserver
