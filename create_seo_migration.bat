@echo off
echo ========================================
echo Creating SEO Fields Migration
echo ========================================

REM Check if virtual environment exists
if not exist "venv" (
    echo Virtual environment not found!
    echo Please run setup.bat first.
    pause
    exit /b 1
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo Creating migration for SEO fields...
python manage.py makemigrations catalogue --name add_seo_fields

if errorlevel 1 (
    echo Migration creation failed!
    pause
    exit /b 1
)

echo.
echo Running migrations...
python manage.py migrate

if errorlevel 1 (
    echo Migration failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo SEO fields migration completed successfully!
echo ========================================
pause
