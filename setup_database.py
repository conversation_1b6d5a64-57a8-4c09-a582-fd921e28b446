#!/usr/bin/env python
"""
Database setup script for Home Services Authentication Service
"""
import os
import sys
import django
from django.conf import settings
from django.core.management import execute_from_command_line
from django.db import connection
from django.core.management.color import no_style
from django.db.utils import IntegrityError

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from django.contrib.auth import get_user_model
from authentication.models import Address, FailedLoginAttempt

User = get_user_model()


def create_sample_data():
    """Create sample data for testing"""
    print("Creating sample data...")
    
    try:
        # Create sample staff user
        if not User.objects.filter(email='<EMAIL>').exists():
            staff_user = User.objects.create_user(
                email='<EMAIL>',
                name='Admin User',
                user_type='STAFF',
                password='admin123',
                is_verified=True,
                is_staff=True,
                is_superuser=True
            )
            print(f"✅ Created staff user: {staff_user.email}")
        
        # Create sample customer
        if not User.objects.filter(mobile_number='+919876543210').exists():
            customer = User.objects.create_user(
                mobile_number='+919876543210',
                name='John Doe',
                user_type='CUSTOMER',
                is_verified=True
            )
            print(f"✅ Created customer: {customer.name}")
            
            # Create sample address for customer
            Address.objects.create(
                user=customer,
                address_type='HOME',
                street='123 Main Street, Apartment 4B',
                city='Mumbai',
                state='Maharashtra',
                zip_code='400001',
                landmark='Near Central Mall',
                is_default=True
            )
            print(f"✅ Created address for customer: {customer.name}")
        
        # Create sample provider
        if not User.objects.filter(mobile_number='+919876543211').exists():
            provider = User.objects.create_user(
                mobile_number='+919876543211',
                name='Jane Smith',
                user_type='PROVIDER',
                is_verified=True
            )
            print(f"✅ Created provider: {provider.name}")
            
            # Create sample address for provider
            Address.objects.create(
                user=provider,
                address_type='WORK',
                street='456 Business District',
                city='Delhi',
                state='Delhi',
                zip_code='110001',
                landmark='Near Metro Station',
                is_default=True
            )
            print(f"✅ Created address for provider: {provider.name}")
        
        print("\n🎉 Sample data created successfully!")
        print("\nSample Login Credentials:")
        print("=" * 50)
        print("Staff Login:")
        print("  Email: <EMAIL>")
        print("  Password: admin123")
        print("\nCustomer Login:")
        print("  Mobile: +919876543210")
        print("  (Use OTP login)")
        print("\nProvider Login:")
        print("  Mobile: +919876543211")
        print("  (Use OTP login)")
        
    except IntegrityError as e:
        print(f"❌ Error creating sample data: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


def check_database_connection():
    """Check if database connection is working"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


def run_migrations():
    """Run database migrations"""
    print("Running database migrations...")
    try:
        execute_from_command_line(['manage.py', 'makemigrations'])
        execute_from_command_line(['manage.py', 'migrate'])
        print("✅ Migrations completed successfully")
        return True
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False


def main():
    """Main setup function"""
    print("🚀 Home Services Authentication Service - Database Setup")
    print("=" * 60)
    
    # Check database connection
    if not check_database_connection():
        print("\n❌ Please ensure PostgreSQL is running and credentials are correct in .env file")
        sys.exit(1)
    
    # Run migrations
    if not run_migrations():
        print("\n❌ Migration failed. Please check the error messages above.")
        sys.exit(1)
    
    # Ask user if they want to create sample data
    create_samples = input("\n🤔 Do you want to create sample data for testing? (y/N): ").lower().strip()
    
    if create_samples in ['y', 'yes']:
        create_sample_data()
    
    print("\n✅ Database setup completed!")
    print("\nNext steps:")
    print("1. Update your .env file with actual MSG91 credentials")
    print("2. Start Redis server")
    print("3. Run: python manage.py runserver")
    print("4. Visit: http://localhost:8000/api/docs/ for API documentation")
    print("5. Visit: http://localhost:8000/admin/ for admin interface")


if __name__ == '__main__':
    main()
