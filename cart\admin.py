from django.contrib import admin
from .models import Cart, CartItem


class CartItemInline(admin.TabularInline):
    """
    Inline admin for cart items.
    """
    model = CartItem
    extra = 0
    readonly_fields = ['added_at', 'updated_at', 'get_total_price', 'get_savings']
    fields = [
        'service_id', 'service_title', 'quantity', 'price_at_add', 'discount_at_add',
        'get_total_price', 'get_savings', 'added_at'
    ]

    def get_total_price(self, obj):
        return f"₹{obj.get_total_price()}"
    get_total_price.short_description = 'Total Price'

    def get_savings(self, obj):
        savings = obj.get_savings()
        return f"₹{savings}" if savings > 0 else "No savings"
    get_savings.short_description = 'Savings'


@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    """
    Admin interface for Cart model.
    """
    list_display = [
        'id', 'get_user_info', 'get_items_count', 'sub_total',
        'total_amount', 'is_active', 'created_at'
    ]
    list_filter = ['is_active', 'created_at', 'updated_at']
    search_fields = ['session_key', 'coupon_code_applied']  # Removed user fields due to cross-database relationship
    readonly_fields = [
        'created_at', 'updated_at', 'get_items_count',
        'get_unique_services_count', 'calculate_subtotal'
    ]
    inlines = [CartItemInline]

    fieldsets = (
        (None, {
            'fields': ('user', 'session_key', 'is_active')
        }),
        ('Totals', {
            'fields': (
                'sub_total', 'tax_amount', 'discount_amount',
                'minimum_order_fee_applied', 'total_amount', 'coupon_code_applied'
            )
        }),
        ('Statistics', {
            'fields': ('get_items_count', 'get_unique_services_count', 'calculate_subtotal'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_user_info(self, obj):
        if obj.user_id:
            return f"User ID: {obj.user_id}"
        return f"Session: {obj.session_key}"
    get_user_info.short_description = 'User/Session'

    def get_items_count(self, obj):
        return obj.get_items_count()
    get_items_count.short_description = 'Total Items'

    def get_unique_services_count(self, obj):
        return obj.get_unique_services_count()
    get_unique_services_count.short_description = 'Unique Services'

    def calculate_subtotal(self, obj):
        return f"₹{obj.calculate_subtotal()}"
    calculate_subtotal.short_description = 'Calculated Subtotal'


@admin.register(CartItem)
class CartItemAdmin(admin.ModelAdmin):
    """
    Admin interface for CartItem model.
    """
    list_display = [
        'id', 'get_cart_user', 'service_id', 'service_title', 'quantity',
        'price_at_add', 'discount_at_add', 'get_total_price', 'added_at'
    ]
    list_filter = ['added_at', 'updated_at']  # Removed service__category due to cross-database relationship
    search_fields = [
        'service_title', 'cart__session_key'  # Use stored service_title instead of cross-database reference
    ]
    readonly_fields = ['added_at', 'updated_at', 'get_total_price', 'get_savings']

    fieldsets = (
        (None, {
            'fields': ('cart', 'service_id', 'service_title', 'service_image_url', 'quantity')
        }),
        ('Pricing', {
            'fields': ('price_at_add', 'discount_at_add', 'get_total_price', 'get_savings')
        }),
        ('Timestamps', {
            'fields': ('added_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_cart_user(self, obj):
        if obj.cart.user_id:
            return f"User ID: {obj.cart.user_id}"
        return f"Session: {obj.cart.session_key}"
    get_cart_user.short_description = 'Cart User'

    def get_total_price(self, obj):
        return f"₹{obj.get_total_price()}"
    get_total_price.short_description = 'Total Price'

    def get_savings(self, obj):
        savings = obj.get_savings()
        return f"₹{savings}" if savings > 0 else "No savings"
    get_savings.short_description = 'Savings'
