# Razorpay Production Setup Guide

## 🚀 Production Deployment Checklist

### 1. **Razorpay Account Setup**

#### Create Razorpay Account
1. Visit [https://razorpay.com](https://razorpay.com)
2. Sign up for a business account
3. Complete KYC verification
4. Activate your account

#### Generate API Keys
1. **Test Keys** (for development):
   - Dashboard → Settings → API Keys
   - Generate Test Key ID and Secret
   - Format: `rzp_test_XXXXXXXXXXXX`

2. **Live Keys** (for production):
   - Complete business verification
   - Dashboard → Settings → API Keys
   - Generate Live Key ID and Secret
   - Format: `rzp_live_XXXXXXXXXXXX`

#### Configure Webhooks
1. Dashboard → Settings → Webhooks
2. **Webhook URL**: `https://yourdomain.com/api/payments/webhook/razorpay/`
3. **Secret**: Generate a strong secret (save this)
4. **Events to Subscribe**:
   - `payment.captured`
   - `payment.failed`
   - `refund.processed`
   - `order.paid`

### 2. **Django Admin Configuration**

#### Access Payment Configuration
```
URL: https://yourdomain.com/admin/payments/paymentconfiguration/
```

#### Configure Settings
1. **Razorpay Test Configuration**:
   ```
   Test Key ID: rzp_test_XXXXXXXXXXXX
   Test Key Secret: your_test_secret_key
   ```

2. **Razorpay Live Configuration**:
   ```
   Live Key ID: rzp_live_XXXXXXXXXXXX
   Live Key Secret: your_live_secret_key
   ```

3. **Webhook Configuration**:
   ```
   Webhook Secret: your_webhook_secret
   ```

4. **Environment Settings**:
   - **Development**: Set to "test"
   - **Production**: Set to "live"

5. **Payment Methods**:
   ```
   Enable Razorpay: ✓ Yes
   Enable COD: ✓ Yes (optional)
   ```

6. **COD Settings**:
   ```
   COD Charge Percentage: 2.5% (example)
   COD Minimum Order: ₹100 (example)
   ```

### 3. **Service Configuration**

#### Configure Partial Payments
1. **Access Services**: `/admin/catalogue/service/`
2. **For each service requiring advance payment**:
   ```
   Requires Partial Payment: ✓ Yes
   Payment Type: Percentage (or Fixed Amount)
   Payment Value: 20 (for 20% advance)
   Description: "Pay 20% advance to book this service"
   ```

#### Example Service Configurations
```
Service: Home Cleaning
- Base Price: ₹1000
- Requires Partial Payment: Yes
- Payment Type: Percentage
- Payment Value: 25
- Result: ₹250 advance, ₹750 remaining

Service: AC Repair
- Base Price: ₹800
- Requires Partial Payment: Yes
- Payment Type: Fixed Amount
- Payment Value: 200
- Result: ₹200 advance, ₹600 remaining
```

### 4. **Environment Variables**

#### Production Environment Setup
```bash
# Database
DATABASE_URL=postgresql://user:password@host:port/dbname

# Django Settings
DEBUG=False
SECRET_KEY=your_production_secret_key
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Razorpay (Optional - can use admin config instead)
RAZORPAY_TEST_KEY_ID=rzp_test_XXXXXXXXXXXX
RAZORPAY_TEST_KEY_SECRET=your_test_secret
RAZORPAY_LIVE_KEY_ID=rzp_live_XXXXXXXXXXXX
RAZORPAY_LIVE_KEY_SECRET=your_live_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret
```

### 5. **Frontend Integration**

#### Next.js Environment Variables
```javascript
// .env.local (development)
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:8000
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_XXXXXXXXXXXX

// .env.production
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_live_XXXXXXXXXXXX
```

#### Payment Integration Code
```javascript
// utils/payment.js
export const initiatePayment = async (orderData) => {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/payments/initiate/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(orderData)
  });
  return response.json();
};

export const handleRazorpayPayment = (paymentData, onSuccess, onFailure) => {
  const options = {
    key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
    amount: paymentData.amount * 100,
    currency: paymentData.currency,
    name: "Your Company Name",
    description: `Order ${paymentData.order_number}`,
    order_id: paymentData.razorpay_order_id,
    handler: async (response) => {
      try {
        const verifyResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/payments/razorpay/callback/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            transaction_id: paymentData.transaction_id,
            razorpay_payment_id: response.razorpay_payment_id,
            razorpay_order_id: response.razorpay_order_id,
            razorpay_signature: response.razorpay_signature
          })
        });
        
        const result = await verifyResponse.json();
        if (result.success) {
          onSuccess(result);
        } else {
          onFailure(result);
        }
      } catch (error) {
        onFailure({ message: 'Payment verification failed' });
      }
    },
    prefill: {
      name: "Customer Name",
      email: "<EMAIL>",
      contact: "9999999999"
    },
    theme: {
      color: "#3399cc"
    }
  };

  const rzp = new Razorpay(options);
  rzp.open();
};
```

### 6. **Testing Workflow**

#### Development Testing
1. **Set Environment**: "test" in admin
2. **Use Test Cards**:
   ```
   Card Number: 4111 1111 1111 1111
   Expiry: Any future date
   CVV: Any 3 digits
   ```
3. **Test Scenarios**:
   - Successful payment
   - Failed payment
   - Partial payment calculation
   - COD workflow

#### Production Testing
1. **Set Environment**: "live" in admin
2. **Test with Small Amounts**: ₹1, ₹10
3. **Verify Webhooks**: Check webhook delivery
4. **Test Refunds**: Process test refunds

### 7. **Security Checklist**

#### Server Security
- ✅ HTTPS enabled for all endpoints
- ✅ API keys stored securely (not in code)
- ✅ Webhook signature verification enabled
- ✅ CORS configured properly
- ✅ Rate limiting enabled

#### Payment Security
- ✅ Never expose live API keys to frontend
- ✅ Always verify payment signatures server-side
- ✅ Validate payment amounts before processing
- ✅ Log all payment transactions
- ✅ Monitor for suspicious activities

### 8. **Monitoring & Alerts**

#### Set Up Monitoring
1. **Payment Success Rate**: Monitor payment completion rates
2. **Failed Payments**: Alert on high failure rates
3. **Webhook Delivery**: Monitor webhook success
4. **Transaction Logs**: Regular log analysis

#### Razorpay Dashboard Monitoring
1. **Daily Reports**: Check payment summaries
2. **Settlement Reports**: Monitor fund settlements
3. **Dispute Management**: Handle payment disputes
4. **Analytics**: Track payment trends

### 9. **Backup & Recovery**

#### Database Backups
```bash
# Backup payment transactions
pg_dump -h host -U user -d database -t payments_* > payments_backup.sql

# Restore if needed
psql -h host -U user -d database < payments_backup.sql
```

#### Configuration Backup
- Export payment configuration from admin
- Save API keys securely
- Document webhook configurations

### 10. **Go-Live Process**

#### Pre-Launch
1. ✅ Complete all testing in test mode
2. ✅ Verify webhook endpoints are accessible
3. ✅ Configure production API keys
4. ✅ Set environment to "live"
5. ✅ Test with small amounts

#### Launch Day
1. **Switch Environment**: Change to "live" in admin
2. **Monitor Closely**: Watch first few transactions
3. **Test Immediately**: Process test transaction
4. **Verify Webhooks**: Confirm webhook delivery

#### Post-Launch
1. **Daily Monitoring**: Check payment reports
2. **Customer Support**: Handle payment queries
3. **Regular Testing**: Periodic payment tests
4. **Performance Review**: Analyze payment metrics

### 11. **Troubleshooting**

#### Common Issues
1. **Payment Signature Verification Failed**:
   - Check API keys are correct
   - Verify webhook secret
   - Ensure HTTPS is used

2. **Webhook Not Received**:
   - Check webhook URL accessibility
   - Verify webhook secret
   - Check Razorpay webhook logs

3. **COD Charges Not Applied**:
   - Verify COD configuration in admin
   - Check COD minimum order settings

#### Debug Steps
1. Check Django logs for errors
2. Verify Razorpay dashboard for transaction status
3. Test webhook delivery manually
4. Validate API key permissions

### 12. **Support Contacts**

#### Razorpay Support
- **Email**: <EMAIL>
- **Phone**: +91-80-6190-6200
- **Documentation**: https://razorpay.com/docs/

#### Internal Support
- Check API documentation: `RAZORPAY_API_DOCUMENTATION.md`
- Run test script: `python test_razorpay_integration.py`
- Review implementation: `RAZORPAY_IMPLEMENTATION_SUMMARY.md`
