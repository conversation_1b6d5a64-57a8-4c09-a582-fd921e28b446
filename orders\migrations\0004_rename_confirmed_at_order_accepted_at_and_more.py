# Generated by Django 4.2.7 on 2025-06-20 10:01

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0003_remove_orderitem_service_orderitem_service_id_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='order',
            old_name='confirmed_at',
            new_name='accepted_at',
        ),
        migrations.AddField(
            model_name='order',
            name='assigned_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='assigned_by',
            field=models.ForeignKey(blank=True, db_constraint=False, limit_choices_to={'user_type': 'staff'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_orders', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='order',
            name='held_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='held_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='held_orders', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='order',
            name='hold_reason',
            field=models.TextField(blank=True, help_text='Reason for putting order on hold', null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='incomplete_work_details',
            field=models.TextField(blank=True, help_text='Details about pending/incomplete work', null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='marked_incomplete_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='marked_incomplete_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='incomplete_orders', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='order',
            name='payment_status',
            field=models.CharField(choices=[('paid_online', 'Paid Online'), ('cod', 'CoD')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='order',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('vendor_assigned', 'Vendor Assigned'), ('cancelled', 'Cancelled'), ('completed_awaiting_payment', 'Completed: Awaiting Payment'), ('completed_payment_received', 'Completed: Payment Received'), ('incomplete_pending_works', 'Incomplete: Pending Works'), ('on_hold', 'On Hold')], default='pending', max_length=30),
        ),
        migrations.AlterField(
            model_name='ordercancellation',
            name='reason',
            field=models.CharField(choices=[('customer_request', 'Customer Request'), ('customer_no_response', 'Customer Not Responding'), ('customer_changed_mind', 'Customer Changed Mind'), ('provider_unavailable', 'Provider Unavailable'), ('provider_cancelled', 'Provider Cancelled'), ('payment_failed', 'Payment Failed'), ('payment_declined', 'Payment Declined by Customer'), ('service_unavailable', 'Service Unavailable'), ('location_inaccessible', 'Location Not Accessible'), ('weather_conditions', 'Weather Conditions'), ('technical_issues', 'Technical Issues'), ('duplicate_order', 'Duplicate Order'), ('fraud_suspected', 'Fraud Suspected'), ('staff_decision', 'Staff Decision'), ('other', 'Other')], max_length=30),
        ),
        migrations.CreateModel(
            name='OrderIncompleteWork',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.CharField(choices=[('material_shortage', 'Material Shortage'), ('equipment_failure', 'Equipment Failure'), ('additional_work_required', 'Additional Work Required'), ('customer_unavailable', 'Customer Unavailable'), ('time_constraints', 'Time Constraints'), ('weather_conditions', 'Weather Conditions'), ('technical_complications', 'Technical Complications'), ('safety_issues', 'Safety Issues'), ('approval_pending', 'Approval Pending'), ('payment_required', 'Payment Required for Additional Work'), ('other', 'Other')], max_length=30)),
                ('pending_work_description', models.TextField(help_text='Detailed description of pending work')),
                ('estimated_completion_time', models.CharField(blank=True, max_length=100, null=True)),
                ('additional_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('marked_at', models.DateTimeField(auto_now_add=True)),
                ('expected_completion_date', models.DateField(blank=True, null=True)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True, null=True)),
                ('marked_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='incomplete_work', to='orders.order')),
            ],
        ),
        migrations.CreateModel(
            name='OrderHold',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.CharField(choices=[('customer_unavailable', 'Customer Unavailable'), ('provider_unavailable', 'Provider Unavailable'), ('payment_pending', 'Payment Pending'), ('material_unavailable', 'Material/Equipment Unavailable'), ('weather_conditions', 'Weather Conditions'), ('technical_issues', 'Technical Issues'), ('customer_request', 'Customer Request'), ('provider_request', 'Provider Request'), ('quality_issues', 'Quality Issues'), ('safety_concerns', 'Safety Concerns'), ('administrative_hold', 'Administrative Hold'), ('other', 'Other')], max_length=30)),
                ('description', models.TextField(blank=True, null=True)),
                ('held_at', models.DateTimeField(auto_now_add=True)),
                ('expected_resolution_date', models.DateField(blank=True, null=True)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True, null=True)),
                ('held_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='hold_details', to='orders.order')),
                ('resolved_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_holds', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
