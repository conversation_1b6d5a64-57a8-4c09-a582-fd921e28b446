#!/usr/bin/env python3
"""
Test orders list endpoint specifically
"""
import requests
import json

def test_orders_list():
    # Login
    login_response = requests.post(
        "http://localhost:8000/api/auth/login/email/",
        json={"email": "<EMAIL>", "password": "admin123"}
    )
    
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.text}")
        return
    
    token = login_response.json()['tokens']['access']
    print("Login successful")
    
    # Test orders list
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        orders_response = requests.get(
            "http://localhost:8000/api/orders/",
            headers=headers,
            timeout=10
        )
        
        print(f"Orders list status: {orders_response.status_code}")
        print(f"Response headers: {dict(orders_response.headers)}")
        
        if orders_response.status_code == 200:
            data = orders_response.json()
            print(f"Orders data type: {type(data)}")
            if isinstance(data, dict):
                print(f"Keys: {list(data.keys())}")
                if 'results' in data:
                    print(f"Orders count: {len(data['results'])}")
                    if data['results']:
                        print(f"First order: {data['results'][0]}")
            elif isinstance(data, list):
                print(f"Orders count: {len(data)}")
                if data:
                    print(f"First order: {data[0]}")
        else:
            print(f"Error response: {orders_response.text[:500]}")
            
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_orders_list()
