from rest_framework import serializers
from rest_framework_recursive.fields import Recurs<PERSON><PERSON>ield
from .models import Category, Service, Discount


class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for Service model with calculated fields and SEO fields.
    """
    current_price = serializers.ReadOnlyField(source='get_current_price')
    discount_percentage = serializers.ReadOnlyField(source='get_discount_percentage')
    category_name = serializers.CharField(source='category.name', read_only=True)

    # SEO helper fields
    seo_title = serializers.ReadOnlyField(source='get_seo_title')
    seo_description = serializers.ReadOnlyField(source='get_seo_description')
    og_image = serializers.ReadOnlyField(source='get_og_image')

    class Meta:
        model = Service
        fields = [
            'id', 'title', 'slug', 'image', 'description',
            'base_price', 'discount_price', 'current_price',
            'discount_percentage', 'time_to_complete',
            'category', 'category_name', 'is_active',
            'meta_title', 'meta_description', 'og_image_url',
            'seo_title', 'seo_description', 'og_image',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['slug', 'created_at', 'updated_at']

    def validate_discount_price(self, value):
        """Ensure discount price is less than base price"""
        if value is not None and hasattr(self.instance, 'base_price'):
            if value >= self.instance.base_price:
                raise serializers.ValidationError(
                    "Discount price must be less than base price."
                )
        return value


class ServiceListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for service listings with basic SEO fields.
    """
    current_price = serializers.ReadOnlyField(source='get_current_price')
    discount_percentage = serializers.ReadOnlyField(source='get_discount_percentage')
    category_name = serializers.CharField(source='category.name', read_only=True)

    # SEO helper fields for listings
    seo_title = serializers.ReadOnlyField(source='get_seo_title')
    seo_description = serializers.ReadOnlyField(source='get_seo_description')

    class Meta:
        model = Service
        fields = [
            'id', 'title', 'slug', 'image', 'base_price',
            'discount_price', 'current_price', 'discount_percentage',
            'category_name', 'time_to_complete',
            'meta_title', 'meta_description', 'og_image_url',
            'seo_title', 'seo_description'
        ]


class CategorySerializer(serializers.ModelSerializer):
    """
    Serializer for Category model with nested children, services, and SEO fields.
    """
    children = RecursiveField(many=True, read_only=True)
    services = ServiceListSerializer(many=True, read_only=True)
    services_count = serializers.SerializerMethodField()
    level = serializers.ReadOnlyField()

    # SEO helper fields
    seo_title = serializers.ReadOnlyField(source='get_seo_title')
    seo_description = serializers.ReadOnlyField(source='get_seo_description')
    og_image = serializers.ReadOnlyField(source='get_og_image')

    class Meta:
        model = Category
        fields = [
            'id', 'name', 'slug', 'image', 'description',
            'parent', 'level', 'children', 'services',
            'services_count', 'is_active',
            'meta_title', 'meta_description', 'og_image_url',
            'seo_title', 'seo_description', 'og_image',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['slug', 'level', 'created_at', 'updated_at']

    def get_services_count(self, obj):
        """Get count of active services in this category"""
        return obj.services.filter(is_active=True).count()


class CategoryListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for category listings without nested data but with SEO fields.
    """
    services_count = serializers.SerializerMethodField()
    parent_name = serializers.CharField(source='parent.name', read_only=True)

    # SEO helper fields
    seo_title = serializers.ReadOnlyField(source='get_seo_title')
    seo_description = serializers.ReadOnlyField(source='get_seo_description')

    class Meta:
        model = Category
        fields = [
            'id', 'name', 'slug', 'image', 'description',
            'parent', 'parent_name', 'level', 'services_count', 'is_active',
            'meta_title', 'meta_description', 'og_image_url',
            'seo_title', 'seo_description'
        ]

    def get_services_count(self, obj):
        """Get count of active services in this category"""
        return obj.services.filter(is_active=True).count()


class DiscountSerializer(serializers.ModelSerializer):
    """
    Serializer for Discount model.
    """
    category_name = serializers.CharField(source='applies_to_category.name', read_only=True)
    service_name = serializers.CharField(source='applies_to_service.title', read_only=True)
    is_valid = serializers.ReadOnlyField(source='is_valid')

    class Meta:
        model = Discount
        fields = [
            'id', 'name', 'code', 'discount_type', 'value',
            'start_date', 'end_date', 'is_active', 'is_valid',
            'applies_to_category', 'category_name',
            'applies_to_service', 'service_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate(self, data):
        """Ensure discount applies to either category or service, not both"""
        category = data.get('applies_to_category')
        service = data.get('applies_to_service')
        
        if not category and not service:
            raise serializers.ValidationError(
                "Discount must apply to either a category or a service."
            )
        if category and service:
            raise serializers.ValidationError(
                "Discount cannot apply to both category and service."
            )
        return data


class CategoryTreeSerializer(serializers.ModelSerializer):
    """
    Serializer for displaying category tree structure.
    """
    children = RecursiveField(many=True, read_only=True)

    class Meta:
        model = Category
        fields = ['id', 'name', 'slug', 'level', 'children']


class ServiceCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating services with SEO fields.
    """
    class Meta:
        model = Service
        fields = [
            'category', 'title', 'image', 'description',
            'base_price', 'discount_price', 'time_to_complete', 'is_active',
            'meta_title', 'meta_description', 'og_image_url'
        ]

    def validate(self, data):
        """Validate service data"""
        base_price = data.get('base_price')
        discount_price = data.get('discount_price')
        
        if discount_price is not None and base_price is not None:
            if discount_price >= base_price:
                raise serializers.ValidationError(
                    "Discount price must be less than base price."
                )
        
        return data


class CategoryCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating categories with SEO fields.
    """
    class Meta:
        model = Category
        fields = [
            'name', 'image', 'description', 'parent', 'is_active',
            'meta_title', 'meta_description', 'og_image_url'
        ]

    def validate_parent(self, value):
        """Prevent circular references in category hierarchy"""
        if value and self.instance:
            if value == self.instance:
                raise serializers.ValidationError("Category cannot be its own parent.")
            
            # Check if the new parent is a descendant of current category
            if value.is_descendant_of(self.instance):
                raise serializers.ValidationError(
                    "Cannot set a descendant category as parent."
                )
        
        return value
