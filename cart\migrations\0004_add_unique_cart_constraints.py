# Generated by Django 4.2.7 on 2025-06-17 06:59

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cart', '0003_add_service_details'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='cartitem',
            name='service',
        ),
        migrations.AddField(
            model_name='cartitem',
            name='service_id',
            field=models.PositiveIntegerField(default=1, help_text='ID of the service from catalogue database'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='cartitem',
            name='price_at_add',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Price of service when added to cart', max_digits=10),
        ),
        migrations.AddConstraint(
            model_name='cart',
            constraint=models.UniqueConstraint(condition=models.Q(('is_active', True), ('user__isnull', False)), fields=('user',), name='unique_active_cart_per_user'),
        ),
        migrations.AddConstraint(
            model_name='cart',
            constraint=models.UniqueConstraint(condition=models.Q(('is_active', True), ('session_key__isnull', False)), fields=('session_key',), name='unique_active_cart_per_session'),
        ),
    ]
