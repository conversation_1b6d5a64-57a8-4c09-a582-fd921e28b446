from django.contrib.auth.backends import BaseBackend
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class EmailBackend(BaseBackend):
    """
    Authentication backend for staff users using email and password
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authenticate staff user with email and password
        """
        if not username or not password:
            return None
        
        try:
            # Only authenticate staff users with email
            user = User.objects.get(email=username, user_type='STAFF')
            
            # Check if account is locked
            if user.is_account_locked():
                logger.warning(f"Authentication attempt on locked account: {username}")
                return None
            
            # Check password
            if user.check_password(password):
                # Reset failed attempts on successful login
                self._reset_failed_attempts(user)
                logger.info(f"Successful email authentication for: {username}")
                return user
            else:
                # Record failed attempt
                self._record_failed_attempt(request, user)
                logger.warning(f"Failed password authentication for: {username}")
                return None
                
        except User.DoesNotExist:
            logger.warning(f"Authentication attempt for non-existent email: {username}")
            return None
    
    def get_user(self, user_id):
        """
        Get user by ID
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
    
    def _record_failed_attempt(self, request, user):
        """
        Record failed login attempt and lock account if necessary
        """
        from .models import FailedLoginAttempt
        
        ip_address = self._get_client_ip(request)
        
        # Create failed attempt record
        FailedLoginAttempt.objects.create(
            user=user,
            email=user.email,
            ip_address=ip_address
        )
        
        # Check if account should be locked
        recent_attempts = FailedLoginAttempt.objects.filter(
            user=user,
            timestamp__gte=timezone.now() - timezone.timedelta(hours=1)
        ).count()
        
        if recent_attempts >= 5:
            user.lock_account(duration_minutes=30)
            logger.warning(f"Account locked due to failed attempts: {user.email}")
    
    def _reset_failed_attempts(self, user):
        """
        Reset failed attempts for user
        """
        from .models import FailedLoginAttempt
        FailedLoginAttempt.objects.filter(user=user).delete()
    
    def _get_client_ip(self, request):
        """
        Get client IP address from request
        """
        if not request:
            return '127.0.0.1'
        
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip


class MobileBackend(BaseBackend):
    """
    Authentication backend for customer/provider users using mobile and OTP
    """
    
    def authenticate(self, request, mobile_number=None, otp=None, **kwargs):
        """
        Authenticate customer/provider user with mobile number and OTP
        """
        if not mobile_number or not otp:
            return None
        
        try:
            # Only authenticate customer/provider users with mobile
            user = User.objects.get(
                mobile_number=mobile_number,
                user_type__in=['CUSTOMER', 'PROVIDER']
            )
            
            # Check if account is locked
            if user.is_account_locked():
                logger.warning(f"Authentication attempt on locked account: {mobile_number}")
                return None
            
            # Verify OTP
            if self._verify_otp(mobile_number, otp):
                # Reset failed attempts on successful login
                self._reset_failed_attempts(user)
                logger.info(f"Successful mobile authentication for: {mobile_number}")
                return user
            else:
                # Record failed attempt
                self._record_failed_attempt(request, user)
                logger.warning(f"Failed OTP authentication for: {mobile_number}")
                return None
                
        except User.DoesNotExist:
            logger.warning(f"Authentication attempt for non-existent mobile: {mobile_number}")
            return None
    
    def get_user(self, user_id):
        """
        Get user by ID
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
    
    def _verify_otp(self, mobile_number, otp):
        """
        Verify OTP from cache
        """
        cache_key = f"otp_{mobile_number}"
        stored_otp = cache.get(cache_key)
        
        if stored_otp and str(stored_otp) == str(otp):
            # Remove OTP from cache after successful verification
            cache.delete(cache_key)
            return True
        return False
    
    def _record_failed_attempt(self, request, user):
        """
        Record failed login attempt and lock account if necessary
        """
        from .models import FailedLoginAttempt
        
        ip_address = self._get_client_ip(request)
        
        # Create failed attempt record
        FailedLoginAttempt.objects.create(
            user=user,
            mobile_number=user.mobile_number,
            ip_address=ip_address
        )
        
        # Check if account should be locked
        recent_attempts = FailedLoginAttempt.objects.filter(
            user=user,
            timestamp__gte=timezone.now() - timezone.timedelta(hours=1)
        ).count()
        
        if recent_attempts >= 5:
            user.lock_account(duration_minutes=30)
            logger.warning(f"Account locked due to failed attempts: {user.mobile_number}")
    
    def _reset_failed_attempts(self, user):
        """
        Reset failed attempts for user
        """
        from .models import FailedLoginAttempt
        FailedLoginAttempt.objects.filter(user=user).delete()
    
    def _get_client_ip(self, request):
        """
        Get client IP address from request
        """
        if not request:
            return '127.0.0.1'
        
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip
