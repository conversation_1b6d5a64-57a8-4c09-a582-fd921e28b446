#!/usr/bin/env python
"""
Test script to verify one cart per user/session functionality
"""
import requests
import json

def test_one_cart_per_session():
    """Test that only one cart exists per session"""
    print("🧪 Testing One Cart Per Session...")
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    try:
        # First request - add item to cart
        response1 = session.post(
            "http://localhost:8000/api/cart/add/",
            json={"service_id": 1, "quantity": 1},
            headers={"Content-Type": "application/json"}
        )
        
        if response1.status_code == 201:
            cart1_id = response1.json()['cart']['id']
            print(f"✅ First request - Cart ID: {cart1_id}")
        else:
            print(f"❌ First request failed: {response1.status_code}")
            return False
        
        # Second request - add different item to cart (should use same cart)
        response2 = session.post(
            "http://localhost:8000/api/cart/add/",
            json={"service_id": 2, "quantity": 1},
            headers={"Content-Type": "application/json"}
        )
        
        if response2.status_code == 201:
            cart2_id = response2.json()['cart']['id']
            print(f"✅ Second request - Cart ID: {cart2_id}")
            
            if cart1_id == cart2_id:
                print("✅ SUCCESS: Same cart used for both requests!")
                
                # Check cart contents
                cart_data = response2.json()['cart']
                print(f"   Items in cart: {cart_data['items_count']}")
                print(f"   Unique services: {cart_data['unique_services_count']}")
                
                return True
            else:
                print("❌ FAIL: Different carts created!")
                return False
        else:
            print(f"❌ Second request failed: {response2.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_cart_api_with_gst():
    """Test cart API with GST breakdown"""
    print("\n🧪 Testing Cart API with GST Breakdown...")
    
    session = requests.Session()
    
    try:
        # Add item to cart
        response = session.post(
            "http://localhost:8000/api/cart/add/",
            json={"service_id": 1, "quantity": 1},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            cart_data = response.json()['cart']
            print("✅ Cart created successfully:")
            print(f"   Cart ID: {cart_data['id']}")
            print(f"   Subtotal: ₹{cart_data['sub_total']}")
            print(f"   CGST: ₹{cart_data['cgst_amount']}")
            print(f"   SGST: ₹{cart_data['sgst_amount']}")
            print(f"   IGST: ₹{cart_data['igst_amount']}")
            print(f"   Service Charge: ₹{cart_data['service_charge']}")
            print(f"   Total: ₹{cart_data['total_amount']}")
            
            # Check tax breakdown
            tax_breakdown = cart_data.get('tax_breakdown', [])
            if tax_breakdown:
                print("   Tax Breakdown:")
                for tax in tax_breakdown:
                    print(f"     - {tax['type']} ({tax['rate']}): ₹{tax['amount']}")
            
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_get_cart():
    """Test get cart endpoint"""
    print("\n🧪 Testing Get Cart Endpoint...")
    
    session = requests.Session()
    
    try:
        # First, add an item to create a cart
        add_response = session.post(
            "http://localhost:8000/api/cart/add/",
            json={"service_id": 1, "quantity": 1},
            headers={"Content-Type": "application/json"}
        )
        
        if add_response.status_code != 201:
            print(f"❌ Failed to create cart: {add_response.status_code}")
            return False
        
        # Now get the cart
        get_response = session.get("http://localhost:8000/api/cart/")
        
        if get_response.status_code == 200:
            cart_data = get_response.json()
            print("✅ Cart retrieved successfully:")
            print(f"   Cart ID: {cart_data['id']}")
            print(f"   Items Count: {cart_data['items_count']}")
            print(f"   Total: ₹{cart_data['total_amount']}")
            
            # Verify GST fields are present
            required_fields = ['cgst_amount', 'sgst_amount', 'igst_amount', 'service_charge', 'tax_breakdown']
            missing_fields = [field for field in required_fields if field not in cart_data]
            
            if missing_fields:
                print(f"❌ Missing GST fields: {missing_fields}")
                return False
            else:
                print("✅ All GST fields present")
                return True
        else:
            print(f"❌ Failed to get cart: {get_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Testing One Cart Per User/Session Functionality")
    print("=" * 60)
    
    # Test 1: One cart per session
    test1_success = test_one_cart_per_session()
    
    # Test 2: Cart API with GST
    test2_success = test_cart_api_with_gst()
    
    # Test 3: Get cart endpoint
    test3_success = test_get_cart()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   One Cart Per Session: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   Cart API with GST: {'✅ PASS' if test2_success else '❌ FAIL'}")
    print(f"   Get Cart Endpoint: {'✅ PASS' if test3_success else '❌ FAIL'}")
    
    if all([test1_success, test2_success, test3_success]):
        print("\n🎉 ALL TESTS PASSED! One cart per user functionality is working correctly.")
    else:
        print("\n⚠️  SOME TESTS FAILED. Check the errors above.")
