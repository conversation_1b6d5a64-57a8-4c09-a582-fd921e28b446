# Generated by Django 4.2.7 on 2025-06-18 09:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0002_alter_order_assigned_provider_alter_order_customer'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='orderitem',
            name='service',
        ),
        migrations.AddField(
            model_name='orderitem',
            name='service_id',
            field=models.PositiveIntegerField(default=1, help_text='ID of the service from catalogue database'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='orderitem',
            name='service_title',
            field=models.CharField(default='Unknown Service', help_text='Service title at time of order', max_length=255),
            preserve_default=False,
        ),
    ]
