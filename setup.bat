@echo off
echo ========================================
echo Home Services Authentication Service
echo Virtual Environment Setup (Windows)
echo ========================================

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Failed to create virtual environment
        pause
        exit /b 1
    )
    echo Virtual environment created successfully!
) else (
    echo Virtual environment already exists.
)

echo.
echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo Installing dependencies...
pip install --upgrade pip
pip install -r requirements.txt

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo To activate the virtual environment manually:
echo   venv\Scripts\activate
echo.
echo To deactivate:
echo   deactivate
echo.
echo Next steps:
echo 1. Update .env file with your database credentials
echo 2. Ask your DB admin to create the 'auth_db' database
echo 3. Run: python manage.py migrate
echo 4. Run: python manage.py createsuperuser
echo 5. Run: python manage.py runserver
echo.
pause
