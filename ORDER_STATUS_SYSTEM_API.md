# Order Status System API Documentation

## Overview

The Home Services platform now implements a comprehensive order status system with the following workflow:

### Order Status Flow
1. **Pending** - Immediately when order is received
2. **Accepted** - Staff changes order status from pending to accepted
3. **Vendor Assigned** - When order is assigned to a vendor/provider
4. **Cancelled** - When staff or customer cancels the order (with reason)
5. **Completed: Awaiting Payment** - Staff/vendor updates when work is completed (CoD orders)
6. **Completed: Payment Received** - When payment is confirmed (Online payments or CoD payment received)
7. **Incomplete: Pending Works** - Vendor marks when work is incomplete with pending tasks
8. **On Hold** - Order is temporarily paused (with reason)

### Payment Status
- **Paid Online** - Payment completed through online gateway
- **CoD** - Cash on Delivery

## API Endpoints

### 1. Accept Order
**Endpoint:** `POST /api/orders/{order_number}/accept/`
**Permission:** Staff only
**Description:** Accept a pending order

**Request Body:**
```json
{
    "notes": "Order reviewed and accepted"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Order accepted successfully",
    "order": {
        "id": "uuid",
        "order_number": "HS20241220001",
        "status": "accepted",
        "accepted_at": "2024-12-20T10:30:00Z",
        ...
    }
}
```

### 2. Assign Vendor/Provider
**Endpoint:** `POST /api/orders/{order_number}/assign-provider/`
**Permission:** Staff only
**Description:** Assign a provider to an accepted order

**Request Body:**
```json
{
    "provider_id": 123,
    "notes": "Assigned to best available provider"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Order assigned to John Doe",
    "order": {
        "status": "vendor_assigned",
        "assigned_provider": 123,
        "assigned_provider_name": "John Doe",
        "assigned_by": 456,
        "assigned_by_name": "Staff Member",
        "assigned_at": "2024-12-20T11:00:00Z",
        ...
    }
}
```

### 3. Complete Order
**Endpoint:** `POST /api/orders/{order_number}/complete/`
**Permission:** Staff or assigned provider
**Description:** Mark order as completed

**Request Body:**
```json
{
    "payment_received": false,
    "notes": "Work completed successfully"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Order completed successfully",
    "order": {
        "status": "completed_awaiting_payment",
        "payment_status": "cod",
        "completed_at": "2024-12-20T15:30:00Z",
        ...
    }
}
```

### 4. Hold Order
**Endpoint:** `POST /api/orders/{order_number}/hold/`
**Permission:** Staff only
**Description:** Put order on hold

**Request Body:**
```json
{
    "reason": "customer_unavailable",
    "description": "Customer not responding to calls",
    "expected_resolution_date": "2024-12-22"
}
```

**Hold Reasons:**
- `customer_unavailable`
- `provider_unavailable`
- `payment_pending`
- `material_unavailable`
- `weather_conditions`
- `technical_issues`
- `customer_request`
- `provider_request`
- `quality_issues`
- `safety_concerns`
- `administrative_hold`
- `other`

### 5. Mark Incomplete
**Endpoint:** `POST /api/orders/{order_number}/mark-incomplete/`
**Permission:** Staff or assigned provider
**Description:** Mark order as incomplete with pending work

**Request Body:**
```json
{
    "reason": "material_shortage",
    "pending_work_description": "Need additional plumbing materials to complete installation",
    "estimated_completion_time": "2-3 hours",
    "additional_cost": 150.00,
    "expected_completion_date": "2024-12-21"
}
```

**Incomplete Reasons:**
- `material_shortage`
- `equipment_failure`
- `additional_work_required`
- `customer_unavailable`
- `time_constraints`
- `weather_conditions`
- `technical_complications`
- `safety_issues`
- `approval_pending`
- `payment_required`
- `other`

### 6. Cancel Order
**Endpoint:** `POST /api/orders/{order_number}/cancel/`
**Permission:** Staff or customer
**Description:** Cancel an order

**Enhanced Cancellation Reasons:**
- `customer_request`
- `customer_no_response`
- `customer_changed_mind`
- `provider_unavailable`
- `provider_cancelled`
- `payment_failed`
- `payment_declined`
- `service_unavailable`
- `location_inaccessible`
- `weather_conditions`
- `technical_issues`
- `duplicate_order`
- `fraud_suspected`
- `staff_decision`
- `other`

### 7. Reschedule Order
**Endpoint:** `POST /api/orders/{order_number}/reschedule/`
**Permission:** Staff or customer
**Description:** Reschedule an order (automatically changes status back to 'accepted')

**Request Body:**
```json
{
    "new_date": "2024-12-22",
    "new_time_slot": "10:00-12:00",
    "reason": "Customer requested different time"
}
```

## Order Model Fields

### New Fields Added:
- `assigned_at` - When provider was assigned
- `assigned_by` - Staff member who assigned the provider
- `hold_reason` - Reason for putting order on hold
- `held_at` - When order was put on hold
- `held_by` - Who put the order on hold
- `incomplete_work_details` - Details about pending work
- `marked_incomplete_at` - When order was marked incomplete
- `marked_incomplete_by` - Who marked the order incomplete

### Updated Fields:
- `status` - New status choices as described above
- `payment_status` - Simplified to 'paid_online' and 'cod'
- `accepted_at` - Renamed from `confirmed_at`

## Status Transition Rules

1. **Pending → Accepted**: Staff only
2. **Accepted → Vendor Assigned**: Staff assigns provider
3. **Vendor Assigned → Completed**: Staff or assigned provider
4. **Any Status → Cancelled**: Staff or customer (with restrictions)
5. **Any Status → On Hold**: Staff only
6. **Vendor Assigned → Incomplete**: Staff or assigned provider
7. **Reschedule**: Resets status to 'Accepted'

## Error Responses

All endpoints return consistent error format:
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field": ["Field-specific error messages"]
    }
}
```

## Status Codes
- `200` - Success
- `400` - Bad Request (validation errors, invalid status transitions)
- `403` - Forbidden (permission denied)
- `404` - Order not found
