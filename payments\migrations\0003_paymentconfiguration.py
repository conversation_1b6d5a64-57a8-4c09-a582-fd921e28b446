# Generated by Django 4.2.7 on 2025-06-17 08:22

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0002_alter_paymenttransaction_user'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('razorpay_test_key_id', models.CharField(blank=True, help_text='Razorpay Test Key ID', max_length=100, null=True)),
                ('razorpay_test_key_secret', models.CharField(blank=True, help_text='Razorpay Test Key Secret', max_length=100, null=True)),
                ('razorpay_live_key_id', models.Char<PERSON>ield(blank=True, help_text='Razorpay Live Key ID', max_length=100, null=True)),
                ('razorpay_live_key_secret', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, help_text='Razorpay Live Key Secret', max_length=100, null=True)),
                ('razorpay_webhook_secret', models.<PERSON>r<PERSON><PERSON>(blank=True, help_text='Razorpay Webhook Secret', max_length=100, null=True)),
                ('active_environment', models.CharField(choices=[('test', 'Test Mode'), ('live', 'Live Mode')], default='test', help_text='Select which environment to use', max_length=10)),
                ('enable_razorpay', models.BooleanField(default=True, help_text='Enable Razorpay online payments')),
                ('enable_cod', models.BooleanField(default=True, help_text='Enable Cash on Delivery')),
                ('cod_charge_percentage', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Additional charge for COD as percentage of order value', max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.00')), django.core.validators.MaxValueValidator(Decimal('100.00'))])),
                ('cod_minimum_order', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Minimum order value for COD', max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Payment Configuration',
                'verbose_name_plural': 'Payment Configurations',
            },
        ),
    ]
