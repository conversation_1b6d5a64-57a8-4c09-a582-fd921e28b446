#!/usr/bin/env python3
"""
Create test data for the staff dashboard
"""
import os
import django
from decimal import Decimal
from datetime import datetime, timedelta
from django.utils import timezone

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from authentication.models import User
from orders.models import Order, OrderItem, OrderStatusHistory
from catalogue.models import Service, Category

def create_test_users():
    """Create test customer users"""
    print("Creating test users...")
    
    # Create test customers
    customers = []
    for i in range(3):
        mobile = f"+9190525550{i:02d}"
        try:
            customer = User.objects.get(mobile_number=mobile)
            print(f"Customer {mobile} already exists")
        except User.DoesNotExist:
            customer = User.objects.create_user(
                mobile_number=mobile,
                name=f"Test Customer {i+1}",
                user_type='CUSTOMER',
                is_verified=True
            )
            print(f"Created customer: {customer.name}")
        customers.append(customer)
    
    return customers

def create_test_services():
    """Create test services"""
    print("Creating test services...")
    
    # Create a test category
    try:
        category = Category.objects.using('catalogue_db').get(name="Test Services")
    except Category.DoesNotExist:
        category = Category.objects.using('catalogue_db').create(
            name="Test Services",
            slug="test-services",
            description="Test services for staff dashboard"
        )
        print(f"Created category: {category.name}")
    
    # Create test services
    services = []
    service_data = [
        {"title": "House Cleaning", "price": "500.00"},
        {"title": "AC Repair", "price": "800.00"},
        {"title": "Plumbing Service", "price": "600.00"},
    ]
    
    for data in service_data:
        try:
            service = Service.objects.using('catalogue_db').get(title=data["title"])
        except Service.DoesNotExist:
            service = Service.objects.using('catalogue_db').create(
                title=data["title"],
                slug=data["title"].lower().replace(" ", "-"),
                description=f"Test {data['title']} service",
                base_price=data["price"],
                category=category,
                time_to_complete="02:00:00"
            )
            print(f"Created service: {service.title}")
        services.append(service)
    
    return services

def create_test_orders(customers, services):
    """Create test orders"""
    print("Creating test orders...")
    
    orders = []
    statuses = ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled']
    
    for i in range(10):
        customer = customers[i % len(customers)]
        service = services[i % len(services)]
        
        # Create order
        order = Order.objects.create(
            customer=customer,
            subtotal=Decimal(str(service.base_price)),
            tax_amount=Decimal(str(service.base_price)) * Decimal('0.18'),  # 18% tax
            total_amount=Decimal(str(service.base_price)) * Decimal('1.18'),
            delivery_address={
                "street": f"Test Address {i+1}",
                "city": "Test City",
                "state": "Test State",
                "zip_code": "123456"
            },
            payment_method='cod' if i % 2 == 0 else 'razorpay',
            status=statuses[i % len(statuses)],
            scheduled_date=(timezone.now() + timedelta(days=i)).date(),
            scheduled_time_slot="10:00-12:00",
            customer_notes=f"Test order {i+1} notes"
        )
        
        # Set timestamps based on status
        if order.status in ['confirmed', 'in_progress', 'completed']:
            order.confirmed_at = timezone.now() - timedelta(hours=i)
        if order.status in ['in_progress', 'completed']:
            order.confirmed_at = timezone.now() - timedelta(hours=i+1)
        if order.status == 'completed':
            order.completed_at = timezone.now() - timedelta(hours=i-1)
        if order.status == 'cancelled':
            order.cancelled_at = timezone.now() - timedelta(hours=i)
        
        order.save()
        
        # Create order item
        OrderItem.objects.create(
            order=order,
            service_id=service.id,
            service_title=service.title,
            quantity=1,
            unit_price=Decimal(str(service.base_price)),
            total_price=Decimal(str(service.base_price)),
            estimated_duration="02:00:00"
        )
        
        # Create status history
        OrderStatusHistory.objects.create(
            order=order,
            new_status=order.status,
            changed_by=customer,
            reason=f"Order {order.status}"
        )
        
        orders.append(order)
        print(f"Created order: {order.order_number} - {order.status}")
    
    return orders

def main():
    print("=== Creating Test Data for Staff Dashboard ===")
    
    # Create test data
    customers = create_test_users()
    services = create_test_services()
    orders = create_test_orders(customers, services)
    
    print(f"\n=== Test Data Created ===")
    print(f"Customers: {len(customers)}")
    print(f"Services: {len(services)}")
    print(f"Orders: {len(orders)}")
    
    # Test the dashboard API
    print(f"\n=== Testing Dashboard API ===")
    from orders.views import order_dashboard
    from django.test import RequestFactory
    from rest_framework.test import force_authenticate
    
    factory = RequestFactory()
    request = factory.get('/api/orders/dashboard/')
    staff_user = User.objects.filter(user_type='STAFF').first()
    force_authenticate(request, user=staff_user)
    
    response = order_dashboard(request)
    print(f"Dashboard API Status: {response.status_code}")
    if response.status_code == 200:
        data = response.data
        print(f"Total orders: {data['total_orders']}")
        print(f"Pending orders: {data['pending_orders']}")
        print(f"Confirmed orders: {data['confirmed_orders']}")
        print(f"In progress orders: {data['in_progress_orders']}")
        print(f"Completed orders: {data['completed_orders']}")
        print(f"Cancelled orders: {data['cancelled_orders']}")
        print(f"Recent orders: {len(data['recent_orders'])}")

if __name__ == "__main__":
    main()
