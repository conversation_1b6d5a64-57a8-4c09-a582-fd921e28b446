from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from django.db import models
from datetime import datetime, timedelta, time
from .models import (
    SlotConfiguration, WorkingShift, HolidaySchedule,
    TimeSlot, SlotBlockage, SlotBooking
)
from .serializers import (
    SlotConfigurationSerializer, WorkingShiftSerializer, HolidayScheduleSerializer,
    TimeSlotSerializer, TimeSlotListSerializer, SlotBlockageSerializer,
    SlotBookingSerializer, CreateBookingSerializer, AvailableSlotsRequestSerializer,
    SlotGenerationSerializer, BulkSlotUpdateSerializer
)
from .services import SlotGenerationService, SlotAvailabilityService


class SlotConfigurationListView(generics.ListCreateAPIView):
    """List and create slot configurations."""
    queryset = SlotConfiguration.objects.all()
    serializer_class = SlotConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        if self.request.user.user_type == 'staff':
            return SlotConfiguration.objects.all()
        return SlotConfiguration.objects.filter(is_active=True)


class SlotConfigurationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete slot configuration."""
    queryset = SlotConfiguration.objects.all()
    serializer_class = SlotConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_permissions(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return [permissions.IsAuthenticated(), permissions.IsAdminUser()]
        return [permissions.IsAuthenticated()]


class WorkingShiftListView(generics.ListCreateAPIView):
    """List and create working shifts."""
    serializer_class = WorkingShiftSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        config_id = self.request.query_params.get('configuration')
        if config_id:
            return WorkingShift.objects.filter(configuration_id=config_id)
        return WorkingShift.objects.all()


class HolidayScheduleListView(generics.ListCreateAPIView):
    """List and create holiday schedules."""
    queryset = HolidaySchedule.objects.filter(is_active=True)
    serializer_class = HolidayScheduleSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = HolidaySchedule.objects.filter(is_active=True)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        
        return queryset.order_by('date')


class TimeSlotListView(generics.ListAPIView):
    """List time slots with filtering."""
    serializer_class = TimeSlotListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = TimeSlot.objects.all()
        
        # Filter by date
        date = self.request.query_params.get('date')
        if date:
            queryset = queryset.filter(date=date)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by availability
        available_only = self.request.query_params.get('available_only')
        if available_only and available_only.lower() == 'true':
            queryset = queryset.filter(
                status='available',
                current_bookings__lt=models.F('max_bookings'),
                date__gte=timezone.now().date()
            )
        
        return queryset.order_by('date', 'start_time')


class TimeSlotDetailView(generics.RetrieveUpdateAPIView):
    """Retrieve and update time slot."""
    queryset = TimeSlot.objects.all()
    serializer_class = TimeSlotSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_permissions(self):
        if self.request.method in ['PUT', 'PATCH']:
            return [permissions.IsAuthenticated(), permissions.IsAdminUser()]
        return [permissions.IsAuthenticated()]


class SlotBookingListView(generics.ListAPIView):
    """List slot bookings."""
    serializer_class = SlotBookingSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = SlotBooking.objects.all()
        
        # Filter by customer (for customer users)
        if self.request.user.user_type == 'customer':
            queryset = queryset.filter(customer_mobile=self.request.user.mobile)
        
        # Filter by order ID
        order_id = self.request.query_params.get('order_id')
        if order_id:
            queryset = queryset.filter(order_id=order_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(booking_status=status_filter)
        
        # Filter by date
        date = self.request.query_params.get('date')
        if date:
            queryset = queryset.filter(time_slot__date=date)
        
        return queryset.order_by('-booked_at')


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_available_slots(request):
    """Get available time slots for a specific date."""
    serializer = AvailableSlotsRequestSerializer(data=request.query_params)
    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    date = serializer.validated_data['date']
    service_duration = serializer.validated_data.get('service_duration_minutes')
    
    try:
        available_slots = SlotAvailabilityService.get_available_slots(
            date=date,
            service_duration_minutes=service_duration
        )
        
        return Response({
            'success': True,
            'date': date,
            'available_slots': TimeSlotListSerializer(available_slots, many=True).data,
            'total_slots': len(available_slots)
        })
    
    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_booking(request):
    """Create a new slot booking."""
    serializer = CreateBookingSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        with transaction.atomic():
            time_slot = TimeSlot.objects.select_for_update().get(
                id=serializer.validated_data['time_slot_id']
            )
            
            # Double-check availability
            quantity = serializer.validated_data.get('quantity', 1)
            if not time_slot.can_book(quantity):
                return Response({
                    'success': False,
                    'message': 'Time slot is no longer available'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Create booking
            booking = SlotBooking.objects.create(
                time_slot=time_slot,
                order_id=serializer.validated_data['order_id'],
                customer_mobile=serializer.validated_data['customer_mobile'],
                customer_name=serializer.validated_data['customer_name'],
                quantity=quantity,
                service_names=serializer.validated_data['service_names'],
                customer_notes=serializer.validated_data.get('customer_notes', ''),
                booking_status='pending'
            )
            
            # Update slot booking count
            time_slot.current_bookings += quantity
            if time_slot.current_bookings >= time_slot.max_bookings:
                time_slot.status = 'booked'
            
            # Add order ID to booked_orders list
            booked_orders = time_slot.booked_orders or []
            if serializer.validated_data['order_id'] not in booked_orders:
                booked_orders.append(serializer.validated_data['order_id'])
                time_slot.booked_orders = booked_orders
            
            time_slot.save()
            
            return Response({
                'success': True,
                'message': 'Booking created successfully',
                'booking': SlotBookingSerializer(booking).data
            }, status=status.HTTP_201_CREATED)
    
    except TimeSlot.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Time slot not found'
        }, status=status.HTTP_404_NOT_FOUND)
    
    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def generate_slots(request):
    """Generate time slots for a date range."""
    serializer = SlotGenerationSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        config = SlotConfiguration.objects.get(
            id=serializer.validated_data['configuration_id']
        )
        
        generated_count = SlotGenerationService.generate_slots(
            configuration=config,
            start_date=serializer.validated_data['start_date'],
            end_date=serializer.validated_data['end_date']
        )
        
        return Response({
            'success': True,
            'message': f'Generated {generated_count} time slots',
            'configuration': config.name,
            'date_range': {
                'start_date': serializer.validated_data['start_date'],
                'end_date': serializer.validated_data['end_date']
            }
        })
    
    except SlotConfiguration.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Slot configuration not found'
        }, status=status.HTTP_404_NOT_FOUND)
    
    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def bulk_update_slots(request):
    """Bulk update time slots."""
    serializer = BulkSlotUpdateSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        slot_ids = serializer.validated_data['slot_ids']
        action = serializer.validated_data['action']
        reason = serializer.validated_data.get('reason', '')
        
        slots = TimeSlot.objects.filter(id__in=slot_ids)
        
        update_data = {}
        if action == 'block':
            update_data = {
                'status': 'blocked',
                'blocked_reason': reason or 'Manually blocked',
                'blocked_by': request.user.username
            }
        elif action == 'unblock':
            update_data = {
                'status': 'available',
                'blocked_reason': '',
                'blocked_by': ''
            }
        elif action == 'maintenance':
            update_data = {
                'status': 'maintenance',
                'blocked_reason': reason or 'Maintenance period',
                'blocked_by': request.user.username
            }
        elif action == 'available':
            update_data = {
                'status': 'available',
                'blocked_reason': '',
                'blocked_by': ''
            }
        
        updated_count = slots.update(**update_data)
        
        return Response({
            'success': True,
            'message': f'Updated {updated_count} time slots',
            'action': action
        })
    
    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Admin API Views for Staff Dashboard
from rest_framework.views import APIView
from django.db.models import Q


class AdminSchedulingDashboardView(APIView):
    """
    Admin view for scheduling dashboard with comprehensive data
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        if not request.user.user_type == 'STAFF':
            return Response(
                {'error': 'Only staff members can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get date range for filtering
        start_date = request.GET.get('start_date', timezone.now().date())
        end_date = request.GET.get('end_date', timezone.now().date() + timedelta(days=7))

        # Get scheduling statistics
        total_slots = TimeSlot.objects.filter(date__range=[start_date, end_date]).count()
        available_slots = TimeSlot.objects.filter(
            date__range=[start_date, end_date],
            status='available'
        ).count()
        booked_slots = TimeSlot.objects.filter(
            date__range=[start_date, end_date],
            status='booked'
        ).count()
        blocked_slots = TimeSlot.objects.filter(
            date__range=[start_date, end_date],
            status='blocked'
        ).count()

        # Get booking statistics
        total_bookings = SlotBooking.objects.filter(
            time_slot__date__range=[start_date, end_date]
        ).count()
        pending_bookings = SlotBooking.objects.filter(
            time_slot__date__range=[start_date, end_date],
            booking_status='pending'
        ).count()
        confirmed_bookings = SlotBooking.objects.filter(
            time_slot__date__range=[start_date, end_date],
            booking_status='confirmed'
        ).count()

        # Get recent bookings
        recent_bookings = SlotBooking.objects.filter(
            time_slot__date__range=[start_date, end_date]
        ).order_by('-booked_at')[:10]

        # Get active configurations
        active_configs = SlotConfiguration.objects.filter(is_active=True).count()

        # Get holidays in range
        holidays = HolidaySchedule.objects.filter(
            date__range=[start_date, end_date],
            is_active=True
        ).count()

        return Response({
            'date_range': {
                'start_date': start_date,
                'end_date': end_date
            },
            'slot_statistics': {
                'total_slots': total_slots,
                'available_slots': available_slots,
                'booked_slots': booked_slots,
                'blocked_slots': blocked_slots,
                'utilization_rate': round((booked_slots / total_slots * 100) if total_slots > 0 else 0, 2)
            },
            'booking_statistics': {
                'total_bookings': total_bookings,
                'pending_bookings': pending_bookings,
                'confirmed_bookings': confirmed_bookings,
                'completion_rate': round((confirmed_bookings / total_bookings * 100) if total_bookings > 0 else 0, 2)
            },
            'configuration_statistics': {
                'active_configurations': active_configs,
                'holidays_in_range': holidays
            },
            'recent_bookings': SlotBookingSerializer(recent_bookings, many=True).data
        })


class AdminSlotBlockageListView(APIView):
    """
    Admin view for listing all slot blockages with filtering and pagination
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        if not request.user.user_type == 'STAFF':
            return Response(
                {'error': 'Only staff members can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get query parameters
        is_active = request.GET.get('is_active')
        blockage_type = request.GET.get('blockage_type')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        search = request.GET.get('search')
        ordering = request.GET.get('ordering', '-created_at')
        limit = int(request.GET.get('limit', 20))
        offset = int(request.GET.get('offset', 0))

        # Start with all slot blockages
        queryset = SlotBlockage.objects.all()

        # Apply filters
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)

        if blockage_type:
            queryset = queryset.filter(blockage_type=blockage_type)

        if start_date:
            queryset = queryset.filter(start_date__gte=start_date)

        if end_date:
            queryset = queryset.filter(end_date__lte=end_date)

        if search:
            queryset = queryset.filter(
                Q(reason__icontains=search) |
                Q(created_by__icontains=search)
            )

        # Apply ordering
        queryset = queryset.order_by(ordering)

        # Get total count
        total_count = queryset.count()

        # Apply pagination
        blockages = queryset[offset:offset + limit]

        # Serialize blockages
        serialized_blockages = []
        for blockage in blockages:
            serialized_blockages.append({
                'id': blockage.id,
                'start_date': blockage.start_date,
                'end_date': blockage.end_date,
                'start_time': blockage.start_time,
                'end_time': blockage.end_time,
                'blockage_type': blockage.blockage_type,
                'reason': blockage.reason,
                'is_active': blockage.is_active,
                'created_by': blockage.created_by,
                'created_at': blockage.created_at,
                'updated_at': blockage.updated_at,
            })

        # Determine next/previous URLs
        next_url = None
        previous_url = None

        if offset + limit < total_count:
            next_url = f"?offset={offset + limit}&limit={limit}"
            if is_active is not None:
                next_url += f"&is_active={is_active}"
            if blockage_type:
                next_url += f"&blockage_type={blockage_type}"
            if start_date:
                next_url += f"&start_date={start_date}"
            if end_date:
                next_url += f"&end_date={end_date}"
            if search:
                next_url += f"&search={search}"
            if ordering != '-created_at':
                next_url += f"&ordering={ordering}"

        if offset > 0:
            prev_offset = max(0, offset - limit)
            previous_url = f"?offset={prev_offset}&limit={limit}"
            if is_active is not None:
                previous_url += f"&is_active={is_active}"
            if blockage_type:
                previous_url += f"&blockage_type={blockage_type}"
            if start_date:
                previous_url += f"&start_date={start_date}"
            if end_date:
                previous_url += f"&end_date={end_date}"
            if search:
                previous_url += f"&search={search}"
            if ordering != '-created_at':
                previous_url += f"&ordering={ordering}"

        return Response({
            'count': total_count,
            'next': next_url,
            'previous': previous_url,
            'results': serialized_blockages
        })
