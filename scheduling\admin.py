from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import (
    SlotConfiguration, WorkingShift, HolidaySchedule, 
    TimeSlot, SlotBlockage, SlotBooking
)


@admin.register(SlotConfiguration)
class SlotConfigurationAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'slot_interval_minutes', 'buffer_time_minutes', 
        'advance_booking_days', 'is_active', 'created_at'
    ]
    list_filter = ['is_active', 'slot_interval_minutes', 'buffer_time_minutes']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Configuration', {
            'fields': ('name', 'is_active')
        }),
        ('Slot Settings', {
            'fields': ('slot_interval_minutes', 'buffer_time_minutes')
        }),
        ('Booking Rules', {
            'fields': ('advance_booking_days', 'same_day_booking_cutoff_hours')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class WorkingShiftInline(admin.TabularInline):
    model = WorkingShift
    extra = 7  # One for each day of the week
    max_num = 7


@admin.register(WorkingShift)
class WorkingShiftAdmin(admin.ModelAdmin):
    list_display = [
        'configuration', 'get_weekday_display', 'start_time', 
        'end_time', 'is_working_day', 'max_bookings_per_slot'
    ]
    list_filter = ['configuration', 'weekday', 'is_working_day']
    ordering = ['configuration', 'weekday']
    
    def get_weekday_display(self, obj):
        return obj.get_weekday_display()
    get_weekday_display.short_description = 'Day'


@admin.register(HolidaySchedule)
class HolidayScheduleAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'date', 'holiday_type', 'is_recurring', 'is_active'
    ]
    list_filter = ['holiday_type', 'is_recurring', 'is_active', 'date']
    search_fields = ['name', 'description']
    date_hierarchy = 'date'
    
    fieldsets = (
        ('Holiday Information', {
            'fields': ('name', 'date', 'holiday_type', 'description')
        }),
        ('Settings', {
            'fields': ('is_recurring', 'is_active')
        }),
    )


@admin.register(TimeSlot)
class TimeSlotAdmin(admin.ModelAdmin):
    list_display = [
        'date', 'start_time', 'end_time', 'status_badge', 
        'booking_info', 'configuration'
    ]
    list_filter = [
        'status', 'configuration', 'date', 'current_bookings'
    ]
    search_fields = ['blocked_reason', 'notes']
    date_hierarchy = 'date'
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Slot Information', {
            'fields': ('id', 'configuration', 'date', 'start_time', 'end_time')
        }),
        ('Availability', {
            'fields': ('status', 'current_bookings', 'max_bookings')
        }),
        ('Booking Details', {
            'fields': ('booked_orders',),
            'classes': ('collapse',)
        }),
        ('Administrative', {
            'fields': ('blocked_reason', 'blocked_by', 'notes'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def status_badge(self, obj):
        colors = {
            'available': 'green',
            'booked': 'blue',
            'blocked': 'red',
            'maintenance': 'orange'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'
    
    def booking_info(self, obj):
        return f"{obj.current_bookings}/{obj.max_bookings}"
    booking_info.short_description = 'Bookings'
    
    actions = ['mark_available', 'mark_blocked', 'mark_maintenance']
    
    def mark_available(self, request, queryset):
        updated = queryset.update(status='available', blocked_reason='', blocked_by='')
        self.message_user(request, f'{updated} slots marked as available.')
    mark_available.short_description = 'Mark selected slots as available'
    
    def mark_blocked(self, request, queryset):
        updated = queryset.update(
            status='blocked', 
            blocked_reason='Manually blocked by admin',
            blocked_by=request.user.username
        )
        self.message_user(request, f'{updated} slots marked as blocked.')
    mark_blocked.short_description = 'Mark selected slots as blocked'
    
    def mark_maintenance(self, request, queryset):
        updated = queryset.update(
            status='maintenance',
            blocked_reason='Maintenance period',
            blocked_by=request.user.username
        )
        self.message_user(request, f'{updated} slots marked for maintenance.')
    mark_maintenance.short_description = 'Mark selected slots for maintenance'


@admin.register(SlotBlockage)
class SlotBlockageAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'blockage_type', 'start_date', 'end_date', 
        'is_recurring', 'is_active', 'blocked_by'
    ]
    list_filter = [
        'blockage_type', 'is_recurring', 'is_active', 
        'recurrence_pattern', 'start_date'
    ]
    search_fields = ['name', 'reason', 'blocked_by']
    date_hierarchy = 'start_date'
    
    fieldsets = (
        ('Blockage Information', {
            'fields': ('name', 'blockage_type', 'reason')
        }),
        ('Date & Time', {
            'fields': ('start_date', 'end_date', 'start_time', 'end_time')
        }),
        ('Recurrence Settings', {
            'fields': ('is_recurring', 'recurrence_pattern', 'recurrence_end_date'),
            'classes': ('collapse',)
        }),
        ('Administrative', {
            'fields': ('blocked_by', 'is_active')
        }),
    )


@admin.register(SlotBooking)
class SlotBookingAdmin(admin.ModelAdmin):
    list_display = [
        'order_id', 'customer_name', 'customer_mobile', 
        'time_slot_info', 'booking_status_badge', 'booked_at'
    ]
    list_filter = [
        'booking_status', 'time_slot__date', 'quantity'
    ]
    search_fields = [
        'order_id', 'customer_name', 'customer_mobile', 
        'service_names'
    ]
    readonly_fields = [
        'id', 'booked_at', 'confirmed_at', 
        'cancelled_at', 'completed_at'
    ]
    date_hierarchy = 'booked_at'
    
    fieldsets = (
        ('Booking Information', {
            'fields': ('id', 'order_id', 'time_slot')
        }),
        ('Customer Details', {
            'fields': ('customer_name', 'customer_mobile')
        }),
        ('Service Details', {
            'fields': ('service_names', 'quantity', 'booking_status')
        }),
        ('Timestamps', {
            'fields': (
                'booked_at', 'confirmed_at', 
                'cancelled_at', 'completed_at'
            ),
            'classes': ('collapse',)
        }),
        ('Notes', {
            'fields': ('customer_notes', 'admin_notes'),
            'classes': ('collapse',)
        }),
    )
    
    def time_slot_info(self, obj):
        return f"{obj.time_slot.date} {obj.time_slot.start_time}-{obj.time_slot.end_time}"
    time_slot_info.short_description = 'Time Slot'
    
    def booking_status_badge(self, obj):
        colors = {
            'pending': 'orange',
            'confirmed': 'green',
            'cancelled': 'red',
            'completed': 'blue',
            'no_show': 'gray'
        }
        color = colors.get(obj.booking_status, 'black')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_booking_status_display()
        )
    booking_status_badge.short_description = 'Status'
    
    actions = ['confirm_bookings', 'cancel_bookings', 'mark_completed']
    
    def confirm_bookings(self, request, queryset):
        updated = queryset.filter(booking_status='pending').update(
            booking_status='confirmed',
            confirmed_at=timezone.now()
        )
        self.message_user(request, f'{updated} bookings confirmed.')
    confirm_bookings.short_description = 'Confirm selected bookings'
    
    def cancel_bookings(self, request, queryset):
        updated = queryset.exclude(booking_status__in=['cancelled', 'completed']).update(
            booking_status='cancelled',
            cancelled_at=timezone.now()
        )
        self.message_user(request, f'{updated} bookings cancelled.')
    cancel_bookings.short_description = 'Cancel selected bookings'
    
    def mark_completed(self, request, queryset):
        updated = queryset.filter(booking_status='confirmed').update(
            booking_status='completed',
            completed_at=timezone.now()
        )
        self.message_user(request, f'{updated} bookings marked as completed.')
    mark_completed.short_description = 'Mark selected bookings as completed'


# Customize admin site header
admin.site.site_header = "Home Services - Scheduling Management"
admin.site.site_title = "Scheduling Admin"
admin.site.index_title = "Scheduling & Slot Management"
