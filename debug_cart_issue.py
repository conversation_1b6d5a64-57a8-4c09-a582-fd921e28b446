#!/usr/bin/env python
"""
Debug script to check cart issues
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

def debug_cart_issues():
    """Debug cart-related issues."""
    print("🔍 Debugging Cart Issues")
    print("=" * 50)
    
    try:
        from cart.models import Cart, CartItem
        from authentication.models import User
        
        # Check all carts
        print("✅ Checking all carts...")
        all_carts = Cart.objects.all()
        print(f"   Total carts: {all_carts.count()}")
        
        for cart in all_carts[:5]:  # Show first 5 carts
            print(f"   Cart ID: {cart.id}")
            print(f"   User: {getattr(cart.user, 'username', 'No user') if cart.user else 'No user'}")
            print(f"   Is Active: {cart.is_active}")
            print(f"   Items Count: {cart.items.count()}")
            print(f"   Created: {cart.created_at}")
            print("   ---")
        
        # Check active carts
        print("✅ Checking active carts...")
        active_carts = Cart.objects.filter(is_active=True)
        print(f"   Active carts: {active_carts.count()}")
        
        # Check carts with items
        print("✅ Checking carts with items...")
        carts_with_items = Cart.objects.filter(items__isnull=False).distinct()
        print(f"   Carts with items: {carts_with_items.count()}")
        
        # Check recent users
        print("✅ Checking recent users...")
        recent_users = User.objects.filter(user_type='customer')[:5]
        for user in recent_users:
            user_carts = Cart.objects.filter(user=user)
            print(f"   User: {getattr(user, 'username', user.id)} - Carts: {user_carts.count()}")

            # Check if user has active cart
            active_cart = Cart.objects.filter(user=user, is_active=True).first()
            if active_cart:
                print(f"     Active Cart ID: {active_cart.id}")
                print(f"     Items: {active_cart.items.count()}")
            else:
                print("     No active cart")
        
        # Check cart items
        print("✅ Checking cart items...")
        all_items = CartItem.objects.all()
        print(f"   Total cart items: {all_items.count()}")
        
        for item in all_items[:3]:  # Show first 3 items
            print(f"   Item: {item.service_title}")
            print(f"   Cart: {item.cart.id}")
            print(f"   Service ID: {item.service_id}")
            print(f"   Quantity: {item.quantity}")
            print("   ---")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_test_cart():
    """Create a test cart for debugging."""
    print("\n🛠️ Creating Test Cart")
    print("=" * 50)
    
    try:
        from cart.models import Cart, CartItem
        from authentication.models import User
        
        # Get a test user
        test_user = User.objects.filter(user_type='customer').first()
        if not test_user:
            print("❌ No customer users found")
            return False
        
        print(f"✅ Using test user: {getattr(test_user, 'username', test_user.id)}")
        
        # Create or get active cart
        cart, created = Cart.objects.get_or_create(
            user=test_user,
            is_active=True,
            defaults={
                'sub_total': 100.00,
                'tax_amount': 18.00,
                'discount_amount': 0.00,
                'minimum_order_fee_applied': 0.00,
                'total_amount': 118.00,
            }
        )
        
        if created:
            print(f"✅ Created new cart: {cart.id}")
        else:
            print(f"✅ Using existing cart: {cart.id}")
        
        # Add a test item if cart is empty
        if not cart.items.exists():
            CartItem.objects.create(
                cart=cart,
                service_id=1,  # Assuming service ID 1 exists
                service_title="Test Service",
                quantity=1,
                price_at_add=100.00,
                discount_at_add=0.00
            )
            print("✅ Added test item to cart")
        
        print(f"✅ Test cart ready:")
        print(f"   Cart ID: {cart.id}")
        print(f"   User: {getattr(cart.user, 'username', cart.user.id)}")
        print(f"   Items: {cart.items.count()}")
        print(f"   Total: {cart.total_amount}")
        
        return cart.id
        
    except Exception as e:
        print(f"❌ Error creating test cart: {e}")
        return False

if __name__ == "__main__":
    print("🔍 CART DEBUGGING TOOL")
    print("=" * 60)
    
    # Debug existing carts
    debug_success = debug_cart_issues()
    
    if debug_success:
        # Create test cart
        test_cart_id = create_test_cart()
        
        if test_cart_id:
            print(f"\n🎯 TEST CART CREATED: {test_cart_id}")
            print("\nYou can now test order creation with this cart ID")
            print(f"Use cart_id: {test_cart_id} in your frontend")
        
        print("\n✅ DEBUGGING COMPLETE")
    else:
        print("\n❌ DEBUGGING FAILED")
    
    sys.exit(0)
