#!/usr/bin/env python
"""
Script to create migration for OrderItem model changes
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

def create_orderitem_migration():
    """Create migration for OrderItem model changes."""
    print("🔧 Creating OrderItem Migration")
    print("=" * 50)
    
    try:
        from django.core.management import call_command
        
        # Create migration for orders app
        print("Creating migration for orders app...")
        call_command('makemigrations', 'orders', verbosity=2)
        
        print("✅ Migration created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating migration: {e}")
        return False

if __name__ == "__main__":
    success = create_orderitem_migration()
    
    if success:
        print("\n🎉 MIGRATION CREATED SUCCESSFULLY!")
        print("\nNext steps:")
        print("1. Run: python manage.py migrate --database=orders_db")
        print("2. Test order creation")
    else:
        print("\n❌ MIGRATION CREATION FAILED")
    
    sys.exit(0 if success else 1)
