#!/usr/bin/env python
"""
Script to verify database connections and table creation.
"""
import os
import django
from django.db import connections
# No need for execute_from_command_line in this script

def verify_databases():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    django.setup() # Initialize Django's settings and apps

    print("🔍 Verifying Database Connections...")

    databases = {
        'default': 'home_services_auth',
        'catalogue_db': 'home_services_catalogue',
        'cart_db': 'home_services_cart',
        'coupons_db': 'home_services_coupons',
        'orders_db': 'home_services_orders',
        'payments_db': 'home_services_payments',
        'providers_db': 'home_services_providers'
    }

    # Ensure all existing connections are closed before starting verification
    # This helps in preventing stale connections to recently dropped/created databases.
    connections.close_all()

    for db_alias, db_name in databases.items():
        print(f"\n📊 Testing {db_alias} ({db_name})...")
        try:
            # Explicitly ensure connection is open and valid for this alias
            connection = connections[db_alias]
            connection.ensure_connection() # Forces a connection if not already open or if broken

            with connection.cursor() as cursor:
                # Test connection by executing a simple query
                cursor.execute("SELECT 1")
                result = cursor.fetchone()

                if result:
                    print(f"    ✅ Connection successful")

                    # Check if tables exist. Added 'BASE TABLE' for robustness.
                    cursor.execute("""
                        SELECT table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
                        ORDER BY table_name
                    """)
                    tables = cursor.fetchall()

                    if tables:
                        print(f"    📋 Tables found: {len(tables)}")
                        # Print only the first few tables for brevity
                        for table in tables[:5]:
                            print(f"      - {table[0]}")
                        if len(tables) > 5:
                            print(f"      ... and {len(tables) - 5} more")
                    else:
                        print(f"    ⚠️ No tables found - migration needed")

                else:
                    print(f"    ❌ Connection successful, but test query failed unexpectedly.")

        except Exception as e:
            print(f"    ❌ Connection or query failed for {db_alias}: {e}")

    print("\n🎯 Database verification completed!")

if __name__ == '__main__':
    verify_databases()