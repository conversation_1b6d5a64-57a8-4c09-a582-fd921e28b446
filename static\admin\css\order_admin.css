/* Enhanced Order Admin Styles */

/* Service selection styling */
.field-service_selection select {
    width: 100%;
    max-width: 300px;
}

/* Order configuration section */
.fieldset-order-configuration {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.fieldset-order-configuration .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.fieldset-order-configuration .form-row > div {
    flex: 1;
    min-width: 200px;
}

/* Enhanced inline styling */
.inline-group .tabular tr.form-row td {
    padding: 8px;
    vertical-align: top;
}

.inline-group .tabular .field-service_selection {
    min-width: 250px;
}

.inline-group .tabular .field-service_title {
    min-width: 200px;
}

.inline-group .tabular .field-unit_price,
.inline-group .tabular .field-discount_per_unit,
.inline-group .tabular .field-total_price {
    min-width: 100px;
}

/* Alert messages */
.alert {
    padding: 10px 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Payment link styling */
.payment-link-info {
    background-color: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.payment-link-info strong {
    color: #1976d2;
}

/* Tax breakdown styling */
.tax-breakdown {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
}

/* Coupon application styling */
.coupon-section {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.coupon-section label {
    font-weight: bold;
    color: #856404;
}

/* Status badges in list view */
.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    color: white;
    text-transform: uppercase;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fieldset-order-configuration .form-row {
        flex-direction: column;
    }
    
    .inline-group .tabular .field-service_selection,
    .inline-group .tabular .field-service_title {
        min-width: auto;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: " (Loading...)";
    color: #666;
    font-style: italic;
}

/* Enhanced form field styling */
.form-row .field-apply_coupon input {
    background-color: #fff3cd;
    border: 2px solid #ffeaa7;
}

.form-row .field-generate_payment_link input[type="checkbox"] {
    transform: scale(1.2);
    margin-right: 8px;
}

.form-row .field-auto_calculate_tax input[type="checkbox"] {
    transform: scale(1.2);
    margin-right: 8px;
}

/* Help text styling */
.help {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
    line-height: 1.3;
}

/* Order totals summary */
.order-totals-summary {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.order-totals-summary h4 {
    margin-top: 0;
    color: #495057;
}

.order-totals-summary .total-line {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #dee2e6;
}

.order-totals-summary .total-line:last-child {
    border-bottom: none;
    font-weight: bold;
    font-size: 16px;
    color: #28a745;
}

/* Service selection dropdown enhancements */
.field-service_selection select option {
    padding: 5px;
}

.field-service_selection select optgroup {
    font-weight: bold;
    color: #495057;
}

/* Admin notes with payment link highlighting */
.field-admin_notes textarea {
    font-family: monospace;
    font-size: 12px;
}

.payment-link-highlight {
    background-color: #e3f2fd;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}
