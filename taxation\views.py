from django.shortcuts import render, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse
from decimal import Decimal
from .models import TaxCategory, GSTRate, TaxConfiguration
from .services import TaxCalculationService


@staff_member_required
def tax_quick_setup(request):
    """
    Quick setup view for tax configuration
    """
    if request.method == 'POST':
        try:
            # Get form data
            category_name = request.POST.get('category_name', 'Home Services')
            cgst_rate = Decimal(request.POST.get('cgst_rate', '9.00'))
            sgst_rate = Decimal(request.POST.get('sgst_rate', '9.00'))
            igst_rate = Decimal(request.POST.get('igst_rate', '18.00'))
            service_charge = Decimal(request.POST.get('service_charge', '2.50'))
            
            # Create or update tax category
            category, created = TaxCategory.objects.get_or_create(
                name=category_name,
                defaults={'description': f'Tax category for {category_name}', 'is_active': True}
            )
            
            # Create or update GST rates
            GSTRate.objects.update_or_create(
                tax_category=category,
                gst_type='CGST',
                defaults={'rate_percentage': cgst_rate, 'is_active': True}
            )
            
            GSTRate.objects.update_or_create(
                tax_category=category,
                gst_type='SGST',
                defaults={'rate_percentage': sgst_rate, 'is_active': True}
            )
            
            GSTRate.objects.update_or_create(
                tax_category=category,
                gst_type='IGST',
                defaults={'rate_percentage': igst_rate, 'is_active': True}
            )
            
            # Create or update tax configuration
            config, created = TaxConfiguration.objects.get_or_create(
                name=f'Default {category_name} Tax Configuration',
                defaults={
                    'description': f'Default tax configuration for {category_name}',
                    'default_tax_category': category,
                    'service_charge_percentage': service_charge,
                    'is_active': True
                }
            )
            
            if not created:
                config.service_charge_percentage = service_charge
                config.is_active = True
                config.save()
            
            # Deactivate other configurations
            TaxConfiguration.objects.filter(is_active=True).exclude(pk=config.pk).update(is_active=False)
            
            messages.success(request, f'Tax configuration updated successfully! CGST: {cgst_rate}%, SGST: {sgst_rate}%, Service Charge: {service_charge}%')
            return redirect('admin:taxation_taxconfiguration_changelist')
            
        except Exception as e:
            messages.error(request, f'Error updating tax configuration: {str(e)}')
    
    # Get current configuration
    current_config = TaxConfiguration.objects.filter(is_active=True).first()
    current_rates = {}
    
    if current_config and current_config.default_tax_category:
        for rate in current_config.default_tax_category.gst_rates.filter(is_active=True):
            current_rates[rate.gst_type] = rate.rate_percentage
    
    context = {
        'current_config': current_config,
        'current_rates': current_rates,
        'title': 'Quick Tax Setup'
    }
    
    return render(request, 'admin/taxation/quick_setup.html', context)


@staff_member_required
def calculate_tax_preview(request):
    """
    AJAX view to preview tax calculation
    """
    if request.method == 'POST':
        try:
            amount = Decimal(request.POST.get('amount', '0'))
            tax_calculation = TaxCalculationService.calculate_total_tax_and_charges(amount)
            
            return JsonResponse({
                'success': True,
                'calculation': {
                    'subtotal': str(tax_calculation['subtotal']),
                    'cgst': str(tax_calculation['cgst']),
                    'sgst': str(tax_calculation['sgst']),
                    'igst': str(tax_calculation['igst']),
                    'service_charge': str(tax_calculation['service_charge']),
                    'total_tax': str(tax_calculation['total_gst']),
                    'grand_total': str(tax_calculation['grand_total'])
                }
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})


# Admin API Views for Staff Dashboard
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q


class AdminTaxCategoryListView(APIView):
    """
    Admin view for listing all tax categories with filtering and pagination
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if not request.user.user_type == 'STAFF':
            return Response(
                {'error': 'Only staff members can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get query parameters
        is_active = request.GET.get('is_active')
        search = request.GET.get('search')
        ordering = request.GET.get('ordering', '-created_at')
        limit = int(request.GET.get('limit', 20))
        offset = int(request.GET.get('offset', 0))

        # Start with all tax categories
        queryset = TaxCategory.objects.all()

        # Apply filters
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        # Apply ordering
        queryset = queryset.order_by(ordering)

        # Get total count
        total_count = queryset.count()

        # Apply pagination
        tax_categories = queryset[offset:offset + limit]

        # Serialize tax categories
        serialized_categories = []
        for category in tax_categories:
            serialized_categories.append({
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'is_active': category.is_active,
                'created_at': category.created_at,
                'updated_at': category.updated_at,
                'gst_rates_count': category.gst_rates.count(),
            })

        # Determine next/previous URLs
        next_url = None
        previous_url = None

        if offset + limit < total_count:
            next_url = f"?offset={offset + limit}&limit={limit}"
            if is_active is not None:
                next_url += f"&is_active={is_active}"
            if search:
                next_url += f"&search={search}"
            if ordering != '-created_at':
                next_url += f"&ordering={ordering}"

        if offset > 0:
            prev_offset = max(0, offset - limit)
            previous_url = f"?offset={prev_offset}&limit={limit}"
            if is_active is not None:
                previous_url += f"&is_active={is_active}"
            if search:
                previous_url += f"&search={search}"
            if ordering != '-created_at':
                previous_url += f"&ordering={ordering}"

        return Response({
            'count': total_count,
            'next': next_url,
            'previous': previous_url,
            'results': serialized_categories
        })


class AdminGSTRateListView(APIView):
    """
    Admin view for listing all GST rates with filtering and pagination
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if not request.user.user_type == 'STAFF':
            return Response(
                {'error': 'Only staff members can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get query parameters
        is_active = request.GET.get('is_active')
        gst_type = request.GET.get('gst_type')
        tax_category_id = request.GET.get('tax_category_id')
        search = request.GET.get('search')
        ordering = request.GET.get('ordering', '-created_at')
        limit = int(request.GET.get('limit', 20))
        offset = int(request.GET.get('offset', 0))

        # Start with all GST rates
        queryset = GSTRate.objects.select_related('tax_category')

        # Apply filters
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)

        if gst_type:
            queryset = queryset.filter(gst_type=gst_type)

        if tax_category_id:
            queryset = queryset.filter(tax_category_id=tax_category_id)

        if search:
            queryset = queryset.filter(
                Q(gst_type__icontains=search) |
                Q(tax_category__name__icontains=search)
            )

        # Apply ordering
        queryset = queryset.order_by(ordering)

        # Get total count
        total_count = queryset.count()

        # Apply pagination
        gst_rates = queryset[offset:offset + limit]

        # Serialize GST rates
        serialized_rates = []
        for rate in gst_rates:
            serialized_rates.append({
                'id': rate.id,
                'tax_category': {
                    'id': rate.tax_category.id,
                    'name': rate.tax_category.name,
                },
                'gst_type': rate.gst_type,
                'rate_percentage': str(rate.rate_percentage),
                'is_active': rate.is_active,
                'created_at': rate.created_at,
                'updated_at': rate.updated_at,
            })

        # Determine next/previous URLs
        next_url = None
        previous_url = None

        if offset + limit < total_count:
            next_url = f"?offset={offset + limit}&limit={limit}"
            if is_active is not None:
                next_url += f"&is_active={is_active}"
            if gst_type:
                next_url += f"&gst_type={gst_type}"
            if tax_category_id:
                next_url += f"&tax_category_id={tax_category_id}"
            if search:
                next_url += f"&search={search}"
            if ordering != '-created_at':
                next_url += f"&ordering={ordering}"

        if offset > 0:
            prev_offset = max(0, offset - limit)
            previous_url = f"?offset={prev_offset}&limit={limit}"
            if is_active is not None:
                previous_url += f"&is_active={is_active}"
            if gst_type:
                previous_url += f"&gst_type={gst_type}"
            if tax_category_id:
                previous_url += f"&tax_category_id={tax_category_id}"
            if search:
                previous_url += f"&search={search}"
            if ordering != '-created_at':
                previous_url += f"&ordering={ordering}"

        return Response({
            'count': total_count,
            'next': next_url,
            'previous': previous_url,
            'results': serialized_rates
        })


class AdminTaxConfigurationListView(APIView):
    """
    Admin view for listing all tax configurations with filtering and pagination
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if not request.user.user_type == 'STAFF':
            return Response(
                {'error': 'Only staff members can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get query parameters
        is_active = request.GET.get('is_active')
        search = request.GET.get('search')
        ordering = request.GET.get('ordering', '-created_at')
        limit = int(request.GET.get('limit', 20))
        offset = int(request.GET.get('offset', 0))

        # Start with all tax configurations
        queryset = TaxConfiguration.objects.select_related('default_tax_category')

        # Apply filters
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search) |
                Q(default_tax_category__name__icontains=search)
            )

        # Apply ordering
        queryset = queryset.order_by(ordering)

        # Get total count
        total_count = queryset.count()

        # Apply pagination
        tax_configs = queryset[offset:offset + limit]

        # Serialize tax configurations
        serialized_configs = []
        for config in tax_configs:
            serialized_configs.append({
                'id': config.id,
                'name': config.name,
                'description': config.description,
                'default_tax_category': {
                    'id': config.default_tax_category.id if config.default_tax_category else None,
                    'name': config.default_tax_category.name if config.default_tax_category else None,
                } if config.default_tax_category else None,
                'service_charge_percentage': str(config.service_charge_percentage),
                'is_active': config.is_active,
                'created_at': config.created_at,
                'updated_at': config.updated_at,
            })

        # Determine next/previous URLs
        next_url = None
        previous_url = None

        if offset + limit < total_count:
            next_url = f"?offset={offset + limit}&limit={limit}"
            if is_active is not None:
                next_url += f"&is_active={is_active}"
            if search:
                next_url += f"&search={search}"
            if ordering != '-created_at':
                next_url += f"&ordering={ordering}"

        if offset > 0:
            prev_offset = max(0, offset - limit)
            previous_url = f"?offset={prev_offset}&limit={limit}"
            if is_active is not None:
                previous_url += f"&is_active={is_active}"
            if search:
                previous_url += f"&search={search}"
            if ordering != '-created_at':
                previous_url += f"&ordering={ordering}"

        return Response({
            'count': total_count,
            'next': next_url,
            'previous': previous_url,
            'results': serialized_configs
        })
