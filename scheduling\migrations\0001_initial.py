# Generated by Django 4.2.7 on 2025-06-17 09:22

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SlotBlockage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(help_text='Blockage description', max_length=200)),
                ('blockage_type', models.CharField(choices=[('single_slot', 'Single Time Slot'), ('time_range', 'Time Range'), ('full_day', 'Full Day'), ('recurring', 'Recurring Pattern')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('start_time', models.TimeField(blank=True, null=True)),
                ('end_time', models.TimeField(blank=True, null=True)),
                ('is_recurring', models.BooleanField(default=False)),
                ('recurrence_pattern', models.CharField(blank=True, choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('custom', 'Custom Pattern')], max_length=20, null=True)),
                ('recurrence_end_date', models.DateField(blank=True, null=True)),
                ('reason', models.TextField(help_text='Reason for blockage')),
                ('blocked_by', models.CharField(help_text='Staff username who created this blockage', max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Slot Blockage',
                'verbose_name_plural': 'Slot Blockages',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SlotConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Configuration name', max_length=100, unique=True)),
                ('slot_interval_minutes', models.IntegerField(choices=[(15, '15 minutes'), (30, '30 minutes'), (60, '1 hour'), (90, '1.5 hours'), (120, '2 hours')], default=60, help_text='Duration of each time slot in minutes')),
                ('buffer_time_minutes', models.IntegerField(choices=[(0, 'No buffer'), (15, '15 minutes'), (30, '30 minutes'), (45, '45 minutes'), (60, '1 hour')], default=15, help_text='Buffer time at the beginning of each shift')),
                ('advance_booking_days', models.IntegerField(default=30, help_text='How many days in advance customers can book', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(365)])),
                ('same_day_booking_cutoff_hours', models.IntegerField(default=2, help_text='Hours before which same-day booking is not allowed', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(24)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Slot Configuration',
                'verbose_name_plural': 'Slot Configurations',
                'ordering': ['-is_active', 'name'],
            },
        ),
        migrations.CreateModel(
            name='TimeSlot',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('date', models.DateField()),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('status', models.CharField(choices=[('available', 'Available'), ('booked', 'Booked'), ('blocked', 'Blocked'), ('maintenance', 'Maintenance')], default='available', max_length=20)),
                ('current_bookings', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)])),
                ('max_bookings', models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)])),
                ('booked_orders', models.JSONField(blank=True, default=list, help_text='List of order IDs')),
                ('blocked_reason', models.CharField(blank=True, max_length=200, null=True)),
                ('blocked_by', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('configuration', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_slots', to='scheduling.slotconfiguration')),
            ],
            options={
                'verbose_name': 'Time Slot',
                'verbose_name_plural': 'Time Slots',
                'ordering': ['date', 'start_time'],
            },
        ),
        migrations.CreateModel(
            name='SlotBooking',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('order_id', models.CharField(help_text='Order ID from orders database', max_length=100)),
                ('customer_mobile', models.CharField(help_text='Customer mobile number', max_length=15)),
                ('customer_name', models.CharField(help_text='Customer name', max_length=100)),
                ('booking_status', models.CharField(choices=[('pending', 'Pending Confirmation'), ('confirmed', 'Confirmed'), ('cancelled', 'Cancelled'), ('completed', 'Completed'), ('no_show', 'No Show')], default='pending', max_length=20)),
                ('quantity', models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)])),
                ('service_names', models.JSONField(default=list, help_text='List of service names')),
                ('booked_at', models.DateTimeField(auto_now_add=True)),
                ('confirmed_at', models.DateTimeField(blank=True, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('customer_notes', models.TextField(blank=True, null=True)),
                ('admin_notes', models.TextField(blank=True, null=True)),
                ('time_slot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='scheduling.timeslot')),
            ],
            options={
                'verbose_name': 'Slot Booking',
                'verbose_name_plural': 'Slot Bookings',
                'ordering': ['-booked_at'],
            },
        ),
        migrations.CreateModel(
            name='HolidaySchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Holiday/closure name', max_length=200)),
                ('date', models.DateField(help_text='Holiday date')),
                ('holiday_type', models.CharField(choices=[('national', 'National Holiday'), ('regional', 'Regional Holiday'), ('company', 'Company Holiday'), ('maintenance', 'Maintenance Day'), ('custom', 'Custom Closure')], default='custom', max_length=20)),
                ('is_recurring', models.BooleanField(default=False, help_text='Repeats every year')),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Holiday Schedule',
                'verbose_name_plural': 'Holiday Schedules',
                'ordering': ['date'],
                'unique_together': {('date', 'name')},
            },
        ),
        migrations.CreateModel(
            name='WorkingShift',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weekday', models.IntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')])),
                ('start_time', models.TimeField(help_text='Shift start time')),
                ('end_time', models.TimeField(help_text='Shift end time')),
                ('is_working_day', models.BooleanField(default=True)),
                ('max_bookings_per_slot', models.IntegerField(default=1, help_text='Maximum bookings allowed per time slot', validators=[django.core.validators.MinValueValidator(1)])),
                ('configuration', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='working_shifts', to='scheduling.slotconfiguration')),
            ],
            options={
                'verbose_name': 'Working Shift',
                'verbose_name_plural': 'Working Shifts',
                'ordering': ['weekday', 'start_time'],
                'unique_together': {('configuration', 'weekday')},
            },
        ),
        migrations.AddIndex(
            model_name='timeslot',
            index=models.Index(fields=['date', 'status'], name='scheduling__date_55f91f_idx'),
        ),
        migrations.AddIndex(
            model_name='timeslot',
            index=models.Index(fields=['configuration', 'date'], name='scheduling__configu_07149a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='timeslot',
            unique_together={('configuration', 'date', 'start_time')},
        ),
        migrations.AddIndex(
            model_name='slotbooking',
            index=models.Index(fields=['order_id'], name='scheduling__order_i_6bd179_idx'),
        ),
        migrations.AddIndex(
            model_name='slotbooking',
            index=models.Index(fields=['customer_mobile'], name='scheduling__custome_6c0a1f_idx'),
        ),
        migrations.AddIndex(
            model_name='slotbooking',
            index=models.Index(fields=['booking_status'], name='scheduling__booking_7a14e4_idx'),
        ),
    ]
