#!/usr/bin/env python3
"""
Test all staff APIs that the Next.js app needs
"""
import requests
import json

def get_auth_token():
    """Get authentication token"""
    login_response = requests.post(
        "http://localhost:8000/api/auth/login/email/",
        json={"email": "<EMAIL>", "password": "admin123"}
    )
    
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.text}")
        return None
    
    return login_response.json()['tokens']['access']

def test_endpoint(name, url, headers, method='GET', data=None):
    """Test a single endpoint"""
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=data)
        
        print(f"{name}: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if isinstance(result, dict):
                if 'results' in result:
                    print(f"  - Found {len(result['results'])} items")
                elif 'count' in result:
                    print(f"  - Count: {result['count']}")
                else:
                    print(f"  - Keys: {list(result.keys())}")
            elif isinstance(result, list):
                print(f"  - Found {len(result)} items")
        else:
            print(f"  - Error: {response.text[:100]}")
        return response.status_code == 200
    except Exception as e:
        print(f"{name}: ERROR - {e}")
        return False

def main():
    print("=== Testing All Staff APIs ===")
    
    # Get token
    token = get_auth_token()
    if not token:
        print("Cannot proceed without valid token")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    base_url = "http://localhost:8000/api"
    
    # Test all endpoints
    endpoints = [
        # Authentication
        ("Profile", f"{base_url}/auth/profile/"),
        ("Users List", f"{base_url}/auth/users/"),
        
        # Orders
        ("Orders Dashboard", f"{base_url}/orders/dashboard/"),
        ("Orders List", f"{base_url}/orders/"),
        
        # Catalogue
        ("Services List", f"{base_url}/catalogue/services/"),
        ("Categories List", f"{base_url}/catalogue/categories/"),
        
        # Providers (if exists)
        ("Providers List", f"{base_url}/providers/"),
        
        # Payments (if exists)
        ("Transactions List", f"{base_url}/payments/transactions/"),
        
        # Scheduling (if exists)
        ("Time Slots", f"{base_url}/scheduling/slots/"),
        
        # Coupons (if exists)
        ("Coupons List", f"{base_url}/coupons/"),
    ]
    
    results = []
    for name, url in endpoints:
        success = test_endpoint(name, url, headers)
        results.append((name, success))
    
    print(f"\n=== Results ===")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    print(f"\n=== Failed Endpoints ===")
    for name, success in results:
        if not success:
            print(f"❌ {name}")
    
    print(f"\n=== Passed Endpoints ===")
    for name, success in results:
        if success:
            print(f"✅ {name}")

if __name__ == "__main__":
    main()
