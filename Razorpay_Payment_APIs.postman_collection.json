{"info": {"_postman_id": "razorpay-payment-apis", "name": "Razorpay Payment APIs - Home Services", "description": "Complete collection for testing Razorpay payment gateway integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Payment Configuration", "item": [{"name": "Get Payment Configuration", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/payments/configuration/", "host": ["{{base_url}}"], "path": ["api", "payments", "configuration", ""]}}, "response": []}]}, {"name": "Partial Payment Calculation", "item": [{"name": "Calculate Partial Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"service_id\": 1,\n      \"quantity\": 1\n    },\n    {\n      \"service_id\": 2,\n      \"quantity\": 2\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/payments/calculate-partial/", "host": ["{{base_url}}"], "path": ["api", "payments", "calculate-partial", ""]}}, "response": []}]}, {"name": "Razorpay Payments", "item": [{"name": "Initiate Razorpay Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"1\",\n  \"amount\": \"500.00\",\n  \"payment_method\": \"razorpay\",\n  \"currency\": \"INR\"\n}"}, "url": {"raw": "{{base_url}}/api/payments/initiate/", "host": ["{{base_url}}"], "path": ["api", "payments", "initiate", ""]}}, "response": []}, {"name": "Verify Razorpay Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"transaction_id\": \"{{transaction_id}}\",\n  \"razorpay_payment_id\": \"pay_test_123456\",\n  \"razorpay_order_id\": \"order_test_123456\",\n  \"razorpay_signature\": \"test_signature\"\n}"}, "url": {"raw": "{{base_url}}/api/payments/razorpay/callback/", "host": ["{{base_url}}"], "path": ["api", "payments", "razorpay", "callback", ""]}}, "response": []}]}, {"name": "COD Payments", "item": [{"name": "Initiate COD Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"1\",\n  \"amount\": \"500.00\",\n  \"payment_method\": \"cod\",\n  \"currency\": \"INR\"\n}"}, "url": {"raw": "{{base_url}}/api/payments/initiate/", "host": ["{{base_url}}"], "path": ["api", "payments", "initiate", ""]}}, "response": []}, {"name": "Confirm COD Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{provider_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"transaction_id\": \"{{transaction_id}}\",\n  \"collected_amount\": \"500.00\",\n  \"collection_notes\": \"Payment collected successfully at delivery\"\n}"}, "url": {"raw": "{{base_url}}/api/payments/cod/confirm/", "host": ["{{base_url}}"], "path": ["api", "payments", "cod", "confirm", ""]}}, "response": []}]}, {"name": "Payment Status & Management", "item": [{"name": "Get Payment Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/payments/status/{{transaction_id}}/", "host": ["{{base_url}}"], "path": ["api", "payments", "status", "{{transaction_id}}", ""]}}, "response": []}, {"name": "List All Transactions (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/payments/transactions/", "host": ["{{base_url}}"], "path": ["api", "payments", "transactions", ""]}}, "response": []}]}, {"name": "Refunds (Admin Only)", "item": [{"name": "Initiate Refund", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"transaction_id\": \"{{transaction_id}}\",\n  \"amount\": \"250.00\",\n  \"reason\": \"Customer requested partial refund\"\n}"}, "url": {"raw": "{{base_url}}/api/payments/refund/", "host": ["{{base_url}}"], "path": ["api", "payments", "refund", ""]}}, "response": []}, {"name": "Get Refund Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/payments/refund/status/{{refund_id}}/", "host": ["{{base_url}}"], "path": ["api", "payments", "refund", "status", "{{refund_id}}", ""]}}, "response": []}]}, {"name": "Webhooks", "item": [{"name": "Razorpay Webhook", "request": {"method": "POST", "header": [{"key": "X-Razorpay-Signature", "value": "webhook_signature", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"payment.captured\",\n  \"payload\": {\n    \"payment\": {\n      \"entity\": {\n        \"id\": \"pay_test_123456\",\n        \"order_id\": \"order_test_123456\",\n        \"amount\": 50000,\n        \"currency\": \"INR\",\n        \"status\": \"captured\"\n      }\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/api/payments/webhook/razorpay/", "host": ["{{base_url}}"], "path": ["api", "payments", "webhook", "razorpay", ""]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000"}, {"key": "auth_token", "value": "your_jwt_token_here"}, {"key": "admin_token", "value": "admin_jwt_token_here"}, {"key": "provider_token", "value": "provider_jwt_token_here"}, {"key": "transaction_id", "value": "TXN20241217123456ABCDEF"}, {"key": "refund_id", "value": "REF20241217123456"}]}