# Failed Login Attempts Management System

## Overview
This system provides comprehensive tools for Django staff to manage failed login attempts and help users who are blocked from requesting OTP due to too many failed attempts.

## 🚨 Problem Solved
When users have too many failed login attempts, they get blocked from:
- Requesting new OTP codes
- Logging into their accounts
- Accessing the system

Staff can now easily clear these failed attempts to restore user access.

## 🛠️ Features Implemented

### 1. Django Admin Actions

#### **User Admin Actions**
In Django Admin → Authentication → Users:

**Action: "Clear failed login attempts (allow OTP requests)"**
- Select one or more users
- Choose this action from the dropdown
- Clears all failed login attempts for selected users
- Resets all rate limits (OTP, login attempts)
- Shows success message with count of cleared attempts

#### **Failed Login Attempt Admin Actions**
In Django Admin → Authentication → Failed Login Attempts:

**Action: "Clear selected failed attempts"**
- Select specific failed attempts
- Clears only the selected records

**Action: "Clear all attempts for these users"**
- Select any failed attempts
- Clears ALL failed attempts for the users associated with selected records
- Resets rate limits for affected users

**Action: "Clear all attempts from these IPs"**
- Select failed attempts from problematic IPs
- Clears ALL failed attempts from those IP addresses

### 2. Management Command

#### **Command: `clear_failed_attempts`**

**Basic Usage:**
```bash
# Clear attempts for specific mobile number
python manage.py clear_failed_attempts --mobile +919876543210

# Clear attempts for specific email
python manage.py clear_failed_attempts --email <EMAIL>

# Clear attempts for specific user ID
python manage.py clear_failed_attempts --user-id 123

# Clear attempts from specific IP
python manage.py clear_failed_attempts --ip *************

# Clear ALL attempts (use with caution)
python manage.py clear_failed_attempts --all

# Clear attempts older than 24 hours
python manage.py clear_failed_attempts --all --older-than 24
```

**Advanced Usage:**
```bash
# Dry run to see what would be cleared
python manage.py clear_failed_attempts --mobile +919876543210 --dry-run

# Clear old attempts (cleanup)
python manage.py clear_failed_attempts --all --older-than 48 --dry-run
```

### 3. Automatic Rate Limit Reset

**What Gets Reset:**
- OTP sending rate limits (`otp_send_{mobile}`)
- Login attempt rate limits (`login_attempt_{email/mobile}`)
- User-specific action limits (`user_action_{user_id}`)

**When It Happens:**
- Automatically when clearing failed attempts via admin actions
- Automatically when using management command
- Uses the new `RateLimitService.clear_user_rate_limits()` utility

## 📋 Usage Scenarios

### Scenario 1: User Can't Request OTP
**Problem:** User calls saying "I can't get OTP on my mobile"
**Solution:**
1. Go to Django Admin → Authentication → Users
2. Search for user by mobile/email/name
3. Select the user
4. Choose "Clear failed login attempts (allow OTP requests)"
5. User can now request OTP again

### Scenario 2: Multiple Users from Same Office
**Problem:** Multiple users from same IP are blocked
**Solution:**
1. Go to Django Admin → Authentication → Failed Login Attempts
2. Filter by IP address
3. Select attempts from that IP
4. Choose "Clear all attempts from these IPs"
5. All users from that IP can access again

### Scenario 3: Bulk Cleanup
**Problem:** Need to clean up old failed attempts
**Solution:**
```bash
# Clean attempts older than 7 days
python manage.py clear_failed_attempts --all --older-than 168
```

### Scenario 4: Specific User Issues
**Problem:** User with ID 123 is having login issues
**Solution:**
```bash
# Clear all attempts for specific user
python manage.py clear_failed_attempts --user-id 123
```

## 🔧 Technical Details

### Database Tables Affected
- `failed_login_attempts` - Failed login attempt records
- Cache keys for rate limiting

### Rate Limit Keys Cleared
- `rate_limit_otp_send_{mobile_number}`
- `rate_limit_login_attempt_{email}`
- `rate_limit_login_attempt_{mobile_number}`
- `rate_limit_user_action_{user_id}`

### Security Features
- Only staff users can access admin actions
- All operations are logged
- Dry-run mode available for testing
- Detailed success/error messages

## 📊 Monitoring & Reporting

### Admin Interface Shows
- Total failed attempts per user
- IP addresses of failed attempts
- Timestamps of attempts
- Associated user information

### Management Command Shows
- Number of attempts found
- Number of attempts cleared
- Number of users affected
- Rate limits reset count

## 🚀 Quick Reference

### Most Common Commands
```bash
# Help a specific user
python manage.py clear_failed_attempts --mobile +919876543210

# Daily cleanup (recommended cron job)
python manage.py clear_failed_attempts --all --older-than 24

# Emergency: Clear everything (use carefully)
python manage.py clear_failed_attempts --all
```

### Admin Quick Actions
1. **Single User Issue:** Users → Select user → "Clear failed attempts"
2. **IP Block Issue:** Failed Attempts → Filter by IP → "Clear attempts from IPs"
3. **Bulk User Issue:** Users → Select multiple → "Clear failed attempts"

## 🔒 Security Considerations

- Only staff users can perform these operations
- All operations are logged in Django admin logs
- Rate limit resets are temporary (new limits apply immediately)
- Failed attempts are permanently deleted (consider backup if needed)

## 🎯 Benefits

✅ **Immediate User Relief** - Users can access their accounts instantly
✅ **Staff Efficiency** - Quick resolution of common support issues
✅ **Bulk Operations** - Handle multiple users or IPs at once
✅ **Automated Cleanup** - Schedule regular cleanup of old attempts
✅ **Detailed Logging** - Track all operations for audit purposes
✅ **Safe Operations** - Dry-run mode prevents accidental deletions

This system significantly reduces support tickets related to login issues and provides staff with powerful tools to manage user access problems efficiently.
