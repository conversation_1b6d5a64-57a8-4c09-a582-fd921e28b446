# Generated by Django 4.2.7 on 2025-06-16 09:42

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TaxCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Tax Category',
                'verbose_name_plural': 'Tax Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='TaxConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('tax_exemption_threshold', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Orders below this amount are tax exempt', max_digits=10)),
                ('round_tax_to_nearest_paisa', models.BooleanField(default=True, help_text='Round tax amounts to nearest paisa (0.01)')),
                ('service_charge_percentage', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Platform service charge percentage', max_digits=5)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('default_tax_category', models.ForeignKey(blank=True, help_text='Default tax category for services without specific category', null=True, on_delete=django.db.models.deletion.SET_NULL, to='taxation.taxcategory')),
            ],
            options={
                'verbose_name': 'Tax Configuration',
                'verbose_name_plural': 'Tax Configurations',
            },
        ),
        migrations.CreateModel(
            name='GSTRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gst_type', models.CharField(choices=[('CGST', 'Central GST'), ('SGST', 'State GST'), ('IGST', 'Integrated GST'), ('UGST', 'Union Territory GST')], max_length=4)),
                ('rate_percentage', models.DecimalField(decimal_places=2, help_text='GST rate as percentage (e.g., 18.00 for 18%)', max_digits=5)),
                ('hsn_code', models.CharField(blank=True, help_text='HSN/SAC code for services', max_length=10, null=True)),
                ('effective_from', models.DateTimeField(default=django.utils.timezone.now)),
                ('effective_until', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tax_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gst_rates', to='taxation.taxcategory')),
            ],
            options={
                'verbose_name': 'GST Rate',
                'verbose_name_plural': 'GST Rates',
                'ordering': ['-effective_from'],
            },
        ),
        migrations.CreateModel(
            name='TaxCalculation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference_type', models.CharField(choices=[('cart', 'Cart'), ('order', 'Order')], default='cart', max_length=20)),
                ('reference_id', models.PositiveIntegerField()),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cgst_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('sgst_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('igst_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('ugst_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('total_tax', models.DecimalField(decimal_places=2, max_digits=10)),
                ('service_charge', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('calculated_at', models.DateTimeField(auto_now_add=True)),
                ('calculation_details', models.JSONField(blank=True, help_text='Detailed breakdown of tax calculation', null=True)),
                ('tax_configuration', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='taxation.taxconfiguration')),
            ],
            options={
                'verbose_name': 'Tax Calculation',
                'verbose_name_plural': 'Tax Calculations',
                'ordering': ['-calculated_at'],
            },
        ),
        migrations.AddConstraint(
            model_name='gstrate',
            constraint=models.UniqueConstraint(fields=('tax_category', 'gst_type', 'effective_from'), name='taxation_gstrate_tax_category_gst_type_effective_from_uniq'),
        ),
        migrations.AddIndex(
            model_name='taxcalculation',
            index=models.Index(fields=['reference_type', 'reference_id'], name='taxation_ta_referen_b8e5b8_idx'),
        ),
    ]
