#!/usr/bin/env python3
"""
Test script to verify all staff APIs are working correctly
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_staff_login():
    """Test staff login and get token"""
    print("Testing staff login...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login/email/", json=login_data)
    print(f"Login Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Login successful for user: {data['user']['name']}")
        print(f"User type: {data['user']['user_type']}")
        return data['tokens']['access']
    else:
        print(f"Login failed: {response.text}")
        return None

def test_orders_dashboard(token):
    """Test orders dashboard endpoint"""
    print("\nTesting orders dashboard...")

    headers = {"Authorization": f"Bearer {token}"}

    # Test the exact endpoint that should work
    url = "http://localhost:8000/api/orders/dashboard/"
    print(f"Testing: {url}")

    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Dashboard working!")
            print(f"Total orders: {data.get('total_orders', 'N/A')}")
            print(f"Pending orders: {data.get('pending_orders', 'N/A')}")
            print(f"Recent orders count: {len(data.get('recent_orders', []))}")
            return True
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def test_orders_list(token):
    """Test orders list endpoint"""
    print("\nTesting orders list...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/orders/", headers=headers)
    
    print(f"Orders List Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if isinstance(data, dict) and 'results' in data:
            print(f"Orders found: {len(data['results'])}")
            print(f"Total count: {data.get('count', 'N/A')}")
        elif isinstance(data, list):
            print(f"Orders found: {len(data)}")
        return True
    else:
        print(f"Orders list failed: {response.text}")
        return False

def test_user_profile(token):
    """Test user profile endpoint"""
    print("\nTesting user profile...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/auth/profile/", headers=headers)
    
    print(f"Profile Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Profile user: {data.get('name', 'N/A')}")
        print(f"User type: {data.get('user_type', 'N/A')}")
        return True
    else:
        print(f"Profile failed: {response.text}")
        return False

def test_users_list(token):
    """Test users list endpoint"""
    print("\nTesting users list...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/auth/users/", headers=headers)
    
    print(f"Users List Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if isinstance(data, dict) and 'results' in data:
            print(f"Users found: {len(data['results'])}")
        elif isinstance(data, list):
            print(f"Users found: {len(data)}")
        return True
    else:
        print(f"Users list failed: {response.text}")
        return False

def main():
    print("=== Staff API Testing ===")
    
    # Test login
    token = test_staff_login()
    if not token:
        print("Cannot proceed without valid token")
        return
    
    # Test all endpoints
    tests = [
        test_user_profile,
        test_orders_dashboard,
        test_orders_list,
        test_users_list,
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func(token)
            results.append(result)
        except Exception as e:
            print(f"Error in {test_func.__name__}: {e}")
            results.append(False)
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✅ All API tests passed!")
    else:
        print("❌ Some API tests failed!")

if __name__ == "__main__":
    main()
