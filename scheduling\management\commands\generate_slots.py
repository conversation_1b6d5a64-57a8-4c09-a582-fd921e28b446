from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from datetime import timedelta
from scheduling.models import SlotConfiguration
from scheduling.services import SlotGenerationService


class Command(BaseCommand):
    help = 'Generate time slots for a specified date range'

    def add_arguments(self, parser):
        parser.add_argument(
            '--config-id',
            type=int,
            required=True,
            help='Slot configuration ID to use for generation'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days to generate slots for (default: 7)'
        )
        parser.add_argument(
            '--start-date',
            type=str,
            help='Start date in YYYY-MM-DD format (default: today)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force generation even if slots already exist'
        )

    def handle(self, *args, **options):
        config_id = options['config_id']
        days = options['days']
        start_date_str = options.get('start_date')
        force = options['force']

        try:
            # Get configuration
            config = SlotConfiguration.objects.get(id=config_id)
            if not config.is_active:
                raise CommandError(f'Configuration "{config.name}" is not active')

            # Determine start date
            if start_date_str:
                try:
                    start_date = timezone.datetime.strptime(start_date_str, '%Y-%m-%d').date()
                except ValueError:
                    raise CommandError('Invalid date format. Use YYYY-MM-DD')
            else:
                start_date = timezone.now().date()

            end_date = start_date + timedelta(days=days - 1)

            self.stdout.write(
                self.style.SUCCESS(
                    f'Generating slots for configuration: {config.name}'
                )
            )
            self.stdout.write(f'Date range: {start_date} to {end_date}')
            self.stdout.write(f'Slot interval: {config.slot_interval_minutes} minutes')
            self.stdout.write(f'Buffer time: {config.buffer_time_minutes} minutes')

            # Check if slots already exist
            if not force:
                from scheduling.models import TimeSlot
                existing_slots = TimeSlot.objects.filter(
                    configuration=config,
                    date__range=[start_date, end_date]
                ).count()

                if existing_slots > 0:
                    self.stdout.write(
                        self.style.WARNING(
                            f'Found {existing_slots} existing slots in date range. '
                            'Use --force to regenerate.'
                        )
                    )
                    return

            # Generate slots
            generated_count = SlotGenerationService.generate_slots(
                configuration=config,
                start_date=start_date,
                end_date=end_date
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully generated {generated_count} time slots'
                )
            )

        except SlotConfiguration.DoesNotExist:
            raise CommandError(f'Slot configuration with ID {config_id} does not exist')
        except Exception as e:
            raise CommandError(f'Error generating slots: {str(e)}')
