# Generated by Django 4.2.7 on 2025-06-14 08:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='category',
            name='meta_description',
            field=models.TextField(blank=True, help_text='SEO Description (max 150-160 characters for optimal display)', null=True),
        ),
        migrations.AddField(
            model_name='category',
            name='meta_title',
            field=models.CharField(blank=True, help_text='SEO Title (max 60-70 characters for optimal display)', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='category',
            name='og_image_url',
            field=models.URLField(blank=True, help_text='Open Graph / Social Share Image URL', max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='meta_description',
            field=models.TextField(blank=True, help_text='SEO Description (max 150-160 characters for optimal display)', null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='meta_title',
            field=models.CharField(blank=True, help_text='SEO Title (max 60-70 characters for optimal display)', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='og_image_url',
            field=models.URLField(blank=True, help_text='Open Graph / Social Share Image URL', max_length=500, null=True),
        ),
    ]
