from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import TaxCategory, GSTRate, TaxConfiguration, TaxCalculation


@admin.register(TaxCategory)
class TaxCategoryAdmin(admin.ModelAdmin):
    """
    Admin interface for Tax Categories
    """
    list_display = ['name', 'description', 'is_active', 'get_gst_rates_count', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_gst_rates_count(self, obj):
        count = obj.gst_rates.filter(is_active=True).count()
        return format_html(
            '<span style="color: {};">{} active rates</span>',
            'green' if count > 0 else 'red',
            count
        )
    get_gst_rates_count.short_description = 'Active GST Rates'


class GSTRateInline(admin.TabularInline):
    """
    Inline admin for GST Rates
    """
    model = GSTRate
    extra = 1
    fields = ['gst_type', 'rate_percentage', 'hsn_code', 'effective_from', 'effective_until', 'is_active']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(GSTRate)
class GSTRateAdmin(admin.ModelAdmin):
    """
    Admin interface for GST Rates
    """
    list_display = [
        'tax_category', 'gst_type', 'rate_percentage', 'hsn_code',
        'effective_from', 'effective_until', 'is_currently_effective', 'is_active'
    ]
    list_filter = ['gst_type', 'is_active', 'effective_from', 'tax_category']
    search_fields = ['tax_category__name', 'hsn_code']
    readonly_fields = ['created_at', 'updated_at', 'is_currently_effective']
    date_hierarchy = 'effective_from'

    fieldsets = (
        (None, {
            'fields': ('tax_category', 'gst_type', 'rate_percentage', 'hsn_code')
        }),
        ('Validity Period', {
            'fields': ('effective_from', 'effective_until', 'is_active', 'is_currently_effective')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def is_currently_effective(self, obj):
        return obj.is_currently_effective()
    is_currently_effective.short_description = 'Currently Effective'
    is_currently_effective.boolean = True

    actions = ['activate_gst_rates', 'deactivate_gst_rates', 'duplicate_gst_rates']

    def activate_gst_rates(self, request, queryset):
        """Activate selected GST rates"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"Activated {updated} GST rate(s).", level='success')
    activate_gst_rates.short_description = "Activate selected GST rates"

    def deactivate_gst_rates(self, request, queryset):
        """Deactivate selected GST rates"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {updated} GST rate(s).", level='warning')
    deactivate_gst_rates.short_description = "Deactivate selected GST rates"

    def duplicate_gst_rates(self, request, queryset):
        """Duplicate selected GST rates for new periods"""
        for rate in queryset:
            rate.pk = None
            rate.effective_from = timezone.now()
            rate.effective_until = None
            rate.save()

        self.message_user(request, f"Duplicated {queryset.count()} GST rate(s) with current date.", level='success')
    duplicate_gst_rates.short_description = "Duplicate rates for new period"


@admin.register(TaxConfiguration)
class TaxConfigurationAdmin(admin.ModelAdmin):
    """
    Admin interface for Tax Configuration
    """
    list_display = [
        'name', 'tax_exemption_threshold', 'default_tax_category',
        'service_charge_percentage', 'is_active', 'updated_at'
    ]
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Tax Settings', {
            'fields': (
                'tax_exemption_threshold', 'default_tax_category',
                'round_tax_to_nearest_paisa'
            )
        }),
        ('Service Charges', {
            'fields': ('service_charge_percentage',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        # Ensure only one active configuration
        if obj.is_active:
            TaxConfiguration.objects.filter(is_active=True).exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)

    actions = ['make_default_tax_config', 'duplicate_tax_config']

    def make_default_tax_config(self, request, queryset):
        """Make selected tax configuration the default (active) one"""
        if queryset.count() != 1:
            self.message_user(request, "Please select exactly one tax configuration to make default.", level='error')
            return

        # Deactivate all configurations
        TaxConfiguration.objects.update(is_active=False)

        # Activate the selected one
        config = queryset.first()
        config.is_active = True
        config.save()

        self.message_user(request, f"'{config.name}' is now the default tax configuration.", level='success')
    make_default_tax_config.short_description = "Set as default tax configuration"

    def duplicate_tax_config(self, request, queryset):
        """Duplicate selected tax configurations"""
        for config in queryset:
            config.pk = None
            config.name = f"{config.name} (Copy)"
            config.is_active = False
            config.save()

        self.message_user(request, f"Duplicated {queryset.count()} tax configuration(s).", level='success')
    duplicate_tax_config.short_description = "Duplicate selected configurations"


@admin.register(TaxCalculation)
class TaxCalculationAdmin(admin.ModelAdmin):
    """
    Admin interface for Tax Calculations (Read-only for audit)
    """
    list_display = [
        'reference_type', 'reference_id', 'subtotal', 'total_tax',
        'service_charge', 'total_amount', 'calculated_at'
    ]
    list_filter = ['reference_type', 'calculated_at']
    search_fields = ['reference_id']
    readonly_fields = [
        'reference_type', 'reference_id', 'subtotal', 'cgst_amount',
        'sgst_amount', 'igst_amount', 'ugst_amount', 'total_tax',
        'service_charge', 'total_amount', 'tax_configuration',
        'calculated_at', 'calculation_details'
    ]

    fieldsets = (
        ('Reference', {
            'fields': ('reference_type', 'reference_id')
        }),
        ('Tax Breakdown', {
            'fields': (
                'subtotal', 'cgst_amount', 'sgst_amount',
                'igst_amount', 'ugst_amount', 'total_tax'
            )
        }),
        ('Final Amounts', {
            'fields': ('service_charge', 'total_amount')
        }),
        ('Configuration', {
            'fields': ('tax_configuration', 'calculated_at')
        }),
        ('Details', {
            'fields': ('calculation_details',),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        """Disable manual addition of tax calculations"""
        return False

    def has_change_permission(self, request, obj=None):
        """Make tax calculations read-only"""
        return False

    def has_delete_permission(self, request, obj=None):
        """Allow deletion for cleanup"""
        return request.user.is_superuser

    actions = ['export_tax_calculations']

    def export_tax_calculations(self, request, queryset):
        """Export selected tax calculations"""
        # This could be implemented to export to CSV/Excel
        self.message_user(request, f"Export functionality for {queryset.count()} calculations")
    export_tax_calculations.short_description = "Export selected tax calculations"
