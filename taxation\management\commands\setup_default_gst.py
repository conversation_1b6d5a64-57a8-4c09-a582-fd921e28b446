from django.core.management.base import BaseCommand
from decimal import Decimal
from taxation.models import TaxCategory, GSTRate, TaxConfiguration


class Command(BaseCommand):
    help = 'Set up default GST configuration for home services'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🏗️  Setting up default GST configuration...')
        )

        # Create default tax category for home services
        home_services_category, created = TaxCategory.objects.get_or_create(
            name='Home Services',
            defaults={
                'description': 'General home services like cleaning, maintenance, repairs',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'✅ Created tax category: {home_services_category.name}')
        else:
            self.stdout.write(f'📋 Tax category already exists: {home_services_category.name}')

        # Create GST rates for home services (18% GST - 9% CGST + 9% SGST)
        cgst_rate, created = GSTRate.objects.get_or_create(
            tax_category=home_services_category,
            gst_type='CGST',
            defaults={
                'rate_percentage': Decimal('9.00'),
                'hsn_code': '998599',  # General services HSN code
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'✅ Created CGST rate: {cgst_rate.rate_percentage}%')
        else:
            self.stdout.write(f'📋 CGST rate already exists: {cgst_rate.rate_percentage}%')

        sgst_rate, created = GSTRate.objects.get_or_create(
            tax_category=home_services_category,
            gst_type='SGST',
            defaults={
                'rate_percentage': Decimal('9.00'),
                'hsn_code': '998599',  # General services HSN code
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'✅ Created SGST rate: {sgst_rate.rate_percentage}%')
        else:
            self.stdout.write(f'📋 SGST rate already exists: {sgst_rate.rate_percentage}%')

        # Create IGST rate for inter-state transactions (18%)
        igst_rate, created = GSTRate.objects.get_or_create(
            tax_category=home_services_category,
            gst_type='IGST',
            defaults={
                'rate_percentage': Decimal('18.00'),
                'hsn_code': '998599',  # General services HSN code
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'✅ Created IGST rate: {igst_rate.rate_percentage}%')
        else:
            self.stdout.write(f'📋 IGST rate already exists: {igst_rate.rate_percentage}%')

        # Create default tax configuration
        tax_config, created = TaxConfiguration.objects.get_or_create(
            name='Default Home Services Tax Configuration',
            defaults={
                'description': 'Default tax configuration for home services platform',
                'tax_exemption_threshold': Decimal('0.00'),  # No exemption threshold
                'default_tax_category': home_services_category,
                'round_tax_to_nearest_paisa': True,
                'service_charge_percentage': Decimal('2.50'),  # 2.5% platform fee
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'✅ Created tax configuration: {tax_config.name}')
        else:
            self.stdout.write(f'📋 Tax configuration already exists: {tax_config.name}')
            # Update to make it active if it's not
            if not tax_config.is_active:
                tax_config.is_active = True
                tax_config.save()
                self.stdout.write('🔄 Activated existing tax configuration')

        # Deactivate other configurations to ensure only one is active
        TaxConfiguration.objects.filter(is_active=True).exclude(pk=tax_config.pk).update(is_active=False)

        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS('🎉 GST Configuration Setup Complete!')
        )
        self.stdout.write('\n📊 Current Configuration:')
        self.stdout.write(f'   • Tax Category: {home_services_category.name}')
        self.stdout.write(f'   • CGST Rate: {cgst_rate.rate_percentage}%')
        self.stdout.write(f'   • SGST Rate: {sgst_rate.rate_percentage}%')
        self.stdout.write(f'   • IGST Rate: {igst_rate.rate_percentage}%')
        self.stdout.write(f'   • Total GST: {cgst_rate.rate_percentage + sgst_rate.rate_percentage}% (Intra-state)')
        self.stdout.write(f'   • Total GST: {igst_rate.rate_percentage}% (Inter-state)')
        self.stdout.write(f'   • Platform Fee: {tax_config.service_charge_percentage}%')
        self.stdout.write(f'   • Tax Exemption: ₹{tax_config.tax_exemption_threshold}')
        
        self.stdout.write('\n🔗 You can now:')
        self.stdout.write('   1. View GST settings in Django Admin → Taxation')
        self.stdout.write('   2. Test cart with tax calculations')
        self.stdout.write('   3. Modify rates as needed')
        
        self.stdout.write('\n💡 Example calculation for ₹1000 service:')
        self.stdout.write(f'   • Service Amount: ₹1000.00')
        self.stdout.write(f'   • CGST (9%): ₹90.00')
        self.stdout.write(f'   • SGST (9%): ₹90.00')
        self.stdout.write(f'   • Platform Fee (2.5%): ₹25.00')
        self.stdout.write(f'   • Total: ₹1205.00')
        
        self.stdout.write('\n' + '='*50)
