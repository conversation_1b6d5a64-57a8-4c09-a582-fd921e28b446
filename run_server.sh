#!/bin/bash

echo "========================================"
echo "Home Services Authentication Service"
echo "Starting Development Server"
echo "========================================"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Virtual environment not found!"
    echo "Please run ./setup.sh first."
    exit 1
fi

echo "Activating virtual environment..."
source venv/bin/activate

echo ""
echo "Checking Django configuration..."
python manage.py check

if [ $? -ne 0 ]; then
    echo "Django configuration check failed!"
    exit 1
fi

echo ""
echo "Starting development server..."
echo "Server will be available at: http://localhost:8000"
echo "API Documentation: http://localhost:8000/api/docs/"
echo "Admin Interface: http://localhost:8000/admin/"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

python manage.py runserver
