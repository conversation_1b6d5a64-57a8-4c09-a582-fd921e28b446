from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django.shortcuts import get_object_or_404
from django.db import transaction, connections
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.admin.views.decorators import staff_member_required
from decimal import Decimal
import json
from cart.models import Cart, CartItem
from catalogue.models import Service
from .models import Order, OrderItem, OrderStatusHistory, OrderCancellation, OrderReschedule, OrderHold, OrderIncompleteWork
from .serializers import (
    OrderSerializer, OrderListSerializer, CreateOrderSerializer,
    OrderStatusUpdateSerializer, OrderCancellationSerializer,
    OrderRescheduleSerializer, OrderStatusHistorySerializer,
    AssignProviderSerializer, OrderPaymentUpdateSerializer,
    CustomerOrderSerializer, ProviderOrderSerializer,
    OrderHoldSerializer, OrderIncompleteWorkSerializer, AcceptOrderSerializer,
    CompleteOrderSerializer, HoldOrderSerializer, MarkIncompleteSerializer
)


class OrderListCreateView(generics.ListCreateAPIView):
    """
    List orders or create new order from cart.
    """
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CreateOrderSerializer
        return OrderListSerializer
    
    def get_queryset(self):
        user = self.request.user
        if user.user_type == 'STAFF':
            return Order.objects.all()
        elif user.user_type == 'PROVIDER':
            return Order.objects.filter(assigned_provider=user)
        else:  # customer
            return Order.objects.filter(customer=user)
    
    def create(self, request, *args, **kwargs):
        """Create order from cart"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        cart_id = serializer.validated_data['cart_id']
        delivery_address = serializer.validated_data['delivery_address']
        payment_method = serializer.validated_data['payment_method']
        
        try:
            # Get user's cart
            cart = Cart.objects.get(
                id=cart_id,
                user=request.user,
                is_active=True
            )
            
            if not cart.items.exists():
                return Response({
                    'success': False,
                    'message': 'Cart is empty'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            with transaction.atomic():
                # Create order
                order = Order.objects.create(
                    customer=request.user,
                    subtotal=cart.sub_total,
                    tax_amount=cart.tax_amount,
                    discount_amount=cart.discount_amount,
                    minimum_order_fee=cart.minimum_order_fee_applied,
                    total_amount=cart.total_amount,
                    coupon_code=cart.coupon_code_applied,
                    coupon_discount=cart.discount_amount,
                    delivery_address=delivery_address,
                    payment_method=payment_method,
                    scheduled_date=serializer.validated_data.get('scheduled_date'),
                    scheduled_time_slot=serializer.validated_data.get('scheduled_time_slot'),
                    customer_notes=serializer.validated_data.get('customer_notes', '')
                )
                
                # Create order items from cart items
                for cart_item in cart.items.all():
                    # Get service details from catalogue database for historical accuracy
                    try:
                        service = Service.objects.using('catalogue_db').get(id=cart_item.service_id)
                        service_title = service.title
                        estimated_duration = getattr(service, 'time_to_complete', None)
                    except Service.DoesNotExist:
                        # Use cart item data if service not found
                        service_title = cart_item.service_title
                        estimated_duration = None

                    OrderItem.objects.create(
                        order=order,
                        service_id=cart_item.service_id,
                        service_title=service_title,
                        quantity=cart_item.quantity,
                        unit_price=cart_item.price_at_add,
                        discount_per_unit=cart_item.discount_at_add,
                        total_price=cart_item.get_total_price(),
                        estimated_duration=estimated_duration
                    )
                
                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    new_status='pending',
                    changed_by=request.user,
                    reason='Order created'
                )
                
                # Handle payment initiation for COD orders
                if serializer.validated_data.get('payment_method') == 'cod':
                    try:
                        # Import here to avoid circular imports
                        from payments.models import PaymentTransaction, CODPayment, PaymentConfiguration

                        # Check if COD is enabled
                        payment_config = PaymentConfiguration.get_active_config()
                        if not payment_config.enable_cod:
                            return Response({
                                'success': False,
                                'message': 'Cash on Delivery is currently disabled'
                            }, status=status.HTTP_400_BAD_REQUEST)

                        # Check minimum order value for COD
                        if order.total_amount < payment_config.cod_minimum_order:
                            return Response({
                                'success': False,
                                'message': f'Minimum order value for COD is ₹{payment_config.cod_minimum_order}'
                            }, status=status.HTTP_400_BAD_REQUEST)

                        # Calculate COD charges
                        from decimal import Decimal
                        cod_charges = (order.total_amount * payment_config.cod_charge_percentage) / Decimal('100.00')
                        total_with_cod = order.total_amount + cod_charges

                        # Update order total to include COD charges
                        order.total_amount = total_with_cod
                        order.save()

                        # Create payment transaction
                        payment_transaction = PaymentTransaction.objects.create(
                            order_id=str(order.id),
                            order_number=order.order_number,
                            user=request.user,
                            payment_method='cod',
                            amount=total_with_cod,
                            currency='INR',
                            status='pending'
                        )

                        # Create COD payment record
                        CODPayment.objects.create(transaction=payment_transaction)

                        # Mark cart as inactive after successful payment setup
                        cart.is_active = False
                        cart.save()

                        return Response({
                            'success': True,
                            'message': 'COD order created successfully',
                            'order': OrderSerializer(order).data,
                            'payment': {
                                'transaction_id': payment_transaction.transaction_id,
                                'payment_method': 'cod',
                                'cod_charges': float(cod_charges),
                                'total_amount': float(total_with_cod)
                            }
                        }, status=status.HTTP_201_CREATED)

                    except Exception as e:
                        # If payment setup fails, delete the order and keep cart active
                        order.delete()
                        return Response({
                            'success': False,
                            'message': f'Payment setup failed: {str(e)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                else:
                    # For non-COD orders (Razorpay), return cart data for payment initiation
                    # Order will be created after successful payment
                    return Response({
                        'success': True,
                        'message': 'Cart prepared for payment',
                        'cart_id': cart.id,
                        'amount': str(cart.total_amount),
                        'payment_method': payment_method,
                        'order_data': {
                            'delivery_address': delivery_address,
                            'scheduled_date': serializer.validated_data.get('scheduled_date'),
                            'scheduled_time_slot': serializer.validated_data.get('scheduled_time_slot'),
                            'customer_notes': serializer.validated_data.get('customer_notes', '')
                        }
                    }, status=status.HTTP_200_OK)
                
        except Cart.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Cart not found'
            }, status=status.HTTP_404_NOT_FOUND)





@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_cod_order(request):
    """
    Simplified COD order creation endpoint.
    Creates order and handles COD payment in one step.
    """
    serializer = CreateOrderSerializer(data=request.data)

    if serializer.is_valid():
        cart_id = serializer.validated_data['cart_id']
        delivery_address = serializer.validated_data['delivery_address']
        customer_notes = serializer.validated_data.get('customer_notes', '')
        scheduled_date = serializer.validated_data.get('scheduled_date')
        scheduled_time_slot = serializer.validated_data.get('scheduled_time_slot')

        try:
            # Get user's cart
            cart = Cart.objects.get(
                id=cart_id,
                user=request.user,
                is_active=True
            )

            if not cart.items.exists():
                return Response({
                    'success': False,
                    'message': 'Cart is empty'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if COD is enabled
            from payments.models import PaymentConfiguration
            payment_config = PaymentConfiguration.get_active_config()

            if not payment_config.enable_cod:
                return Response({
                    'success': False,
                    'message': 'Cash on Delivery is currently disabled'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check minimum order value for COD
            if cart.total_amount < payment_config.cod_minimum_order:
                return Response({
                    'success': False,
                    'message': f'Minimum order value for COD is ₹{payment_config.cod_minimum_order}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Calculate COD charges
            from decimal import Decimal
            cod_charges = (cart.total_amount * payment_config.cod_charge_percentage) / Decimal('100.00')
            total_with_cod = cart.total_amount + cod_charges

            with transaction.atomic():
                # Create order with COD charges included
                order = Order.objects.create(
                    customer=request.user,
                    subtotal=cart.sub_total,
                    tax_amount=cart.tax_amount,
                    discount_amount=cart.discount_amount,
                    minimum_order_fee=cart.minimum_order_fee_applied,
                    total_amount=total_with_cod,  # Include COD charges
                    coupon_code=cart.coupon_code_applied,
                    coupon_discount=cart.discount_amount,
                    delivery_address=delivery_address,
                    payment_method='cod',
                    scheduled_date=scheduled_date,
                    scheduled_time_slot=scheduled_time_slot,
                    customer_notes=customer_notes
                )

                # Create order items from cart items
                for cart_item in cart.items.all():
                    # Get service details from catalogue database for historical accuracy
                    try:
                        service = Service.objects.using('catalogue_db').get(id=cart_item.service_id)
                        service_title = service.title
                        estimated_duration = getattr(service, 'time_to_complete', None)
                    except Service.DoesNotExist:
                        # Use cart item data if service not found
                        service_title = cart_item.service_title
                        estimated_duration = None

                    OrderItem.objects.create(
                        order=order,
                        service_id=cart_item.service_id,
                        service_title=service_title,
                        quantity=cart_item.quantity,
                        unit_price=cart_item.price_at_add,
                        discount_per_unit=cart_item.discount_at_add,
                        total_price=cart_item.get_total_price(),
                        estimated_duration=estimated_duration
                    )

                # Create payment transaction
                from payments.models import PaymentTransaction, CODPayment
                transaction_obj = PaymentTransaction.objects.create(
                    order_id=str(order.id),
                    order_number=order.order_number,
                    user=request.user,
                    payment_method='cod',
                    amount=total_with_cod,
                    currency='INR',
                    status='pending'
                )

                # Create COD payment record
                CODPayment.objects.create(transaction=transaction_obj)

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    new_status='pending',
                    changed_by=request.user,
                    reason='COD order created'
                )

                # Mark cart as inactive
                cart.is_active = False
                cart.save()

                return Response({
                    'success': True,
                    'message': 'COD order created successfully',
                    'order': OrderSerializer(order).data,
                    'payment': {
                        'transaction_id': transaction_obj.transaction_id,
                        'payment_method': 'cod',
                        'cod_charges': float(cod_charges),
                        'original_amount': float(cart.total_amount),
                        'total_amount': float(total_with_cod)
                    }
                }, status=status.HTTP_201_CREATED)

        except Cart.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Cart not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Order creation failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


class OrderDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete an order.
    """
    permission_classes = [IsAuthenticated]
    lookup_field = 'order_number'
    
    def get_serializer_class(self):
        user = self.request.user
        if user.user_type == 'STAFF':
            return OrderSerializer
        elif user.user_type == 'PROVIDER':
            return ProviderOrderSerializer
        else:  # customer
            return CustomerOrderSerializer
    
    def get_queryset(self):
        user = self.request.user
        if user.user_type == 'STAFF':
            return Order.objects.all()
        elif user.user_type == 'PROVIDER':
            return Order.objects.filter(assigned_provider=user)
        else:  # customer
            return Order.objects.filter(customer=user)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_order_status(request, order_number):
    """
    Update order status (Admin and Provider only).
    """
    if request.user.user_type not in ['staff', 'provider']:
        return Response({
            'success': False,
            'message': 'Permission denied'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        if request.user.user_type == 'staff':
            order = Order.objects.get(order_number=order_number)
        else:  # provider
            order = Order.objects.get(order_number=order_number, assigned_provider=request.user)
        
        serializer = OrderStatusUpdateSerializer(data=request.data)
        if serializer.is_valid():
            previous_status = order.status
            new_status = serializer.validated_data['status']
            reason = serializer.validated_data.get('reason', '')
            admin_notes = serializer.validated_data.get('admin_notes', '')
            
            # Update order
            order.status = new_status
            if admin_notes:
                order.admin_notes = admin_notes
            
            # Set timestamps based on status
            if new_status == 'confirmed' and not order.confirmed_at:
                order.confirmed_at = timezone.now()
            elif new_status == 'completed' and not order.completed_at:
                order.completed_at = timezone.now()
            elif new_status == 'cancelled' and not order.cancelled_at:
                order.cancelled_at = timezone.now()
            
            order.save()
            
            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status=previous_status,
                new_status=new_status,
                changed_by=request.user,
                reason=reason
            )
            
            return Response({
                'success': True,
                'message': f'Order status updated to {new_status}',
                'order': OrderSerializer(order).data
            })
        
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cancel_order(request, order_number):
    """
    Cancel an order.
    """
    try:
        if request.user.user_type == 'staff':
            order = Order.objects.get(order_number=order_number)
        else:  # customer
            order = Order.objects.get(order_number=order_number, customer=request.user)
        
        if not order.can_be_cancelled():
            return Response({
                'success': False,
                'message': 'Order cannot be cancelled at this stage'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        reason = request.data.get('reason', 'customer_request')
        description = request.data.get('description', '')
        
        with transaction.atomic():
            # Update order status
            order.status = 'cancelled'
            order.cancelled_at = timezone.now()
            order.save()
            
            # Create cancellation record
            OrderCancellation.objects.create(
                order=order,
                reason=reason,
                description=description,
                cancelled_by=request.user
            )
            
            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status=order.status,
                new_status='cancelled',
                changed_by=request.user,
                reason=f'Order cancelled: {reason}'
            )
        
        return Response({
            'success': True,
            'message': 'Order cancelled successfully',
            'order': OrderSerializer(order).data
        })
        
    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def assign_provider(request, order_number):
    """
    Assign service provider to order (Admin only).
    """
    try:
        order = Order.objects.get(order_number=order_number)
        
        serializer = AssignProviderSerializer(data=request.data)
        if serializer.is_valid():
            provider_id = serializer.validated_data['provider_id']
            notes = serializer.validated_data.get('notes', '')
            
            from authentication.models import User
            provider = User.objects.get(id=provider_id, user_type='provider')
            
            # Update order
            order.assigned_provider = provider
            order.assigned_by = request.user
            order.assigned_at = timezone.now()
            order.status = 'vendor_assigned'
            if notes:
                order.admin_notes = notes
            order.save()
            
            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status='accepted',
                new_status='vendor_assigned',
                changed_by=request.user,
                reason=f'Assigned to provider: {provider.get_full_name()}'
            )
            
            return Response({
                'success': True,
                'message': f'Order assigned to {provider.get_full_name()}',
                'order': OrderSerializer(order).data
            })
        
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except User.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Provider not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def reschedule_order(request, order_number):
    """
    Request order rescheduling.
    """
    try:
        if request.user.user_type == 'staff':
            order = Order.objects.get(order_number=order_number)
        else:  # customer
            order = Order.objects.get(order_number=order_number, customer=request.user)

        if not order.can_be_rescheduled():
            return Response({
                'success': False,
                'message': 'Order cannot be rescheduled at this stage'
            }, status=status.HTTP_400_BAD_REQUEST)

        new_date = request.data.get('new_date')
        new_time_slot = request.data.get('new_time_slot')
        reason = request.data.get('reason', '')

        if not new_date or not new_time_slot:
            return Response({
                'success': False,
                'message': 'New date and time slot are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create reschedule request
        reschedule = OrderReschedule.objects.create(
            order=order,
            original_date=order.scheduled_date,
            original_time_slot=order.scheduled_time_slot,
            new_date=new_date,
            new_time_slot=new_time_slot,
            reason=reason,
            requested_by=request.user,
            approved=request.user.user_type == 'staff'  # Auto-approve for staff
        )

        if request.user.user_type == 'staff':
            # Update order immediately for staff
            previous_status = order.status
            order.scheduled_date = new_date
            order.scheduled_time_slot = new_time_slot
            order.status = 'accepted'  # Reset to accepted when rescheduled
            order.save()
            reschedule.approved_by = request.user
            reschedule.save()

            # Create status history if status changed
            if previous_status != 'accepted':
                OrderStatusHistory.objects.create(
                    order=order,
                    previous_status=previous_status,
                    new_status='accepted',
                    changed_by=request.user,
                    reason='Order rescheduled - status reset to accepted'
                )

        return Response({
            'success': True,
            'message': 'Reschedule request created successfully',
            'reschedule': OrderRescheduleSerializer(reschedule).data
        })

    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_payment_status(request, order_number):
    """
    Update payment status (usually called by payment gateway).
    """
    try:
        order = Order.objects.get(order_number=order_number)

        # Only allow customer or staff to update payment
        if request.user.user_type not in ['staff'] and order.customer != request.user:
            return Response({
                'success': False,
                'message': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = OrderPaymentUpdateSerializer(data=request.data)
        if serializer.is_valid():
            order.payment_id = serializer.validated_data['payment_id']
            order.payment_signature = serializer.validated_data.get('payment_signature', '')
            order.payment_status = serializer.validated_data['payment_status']

            # Update order status based on payment
            if order.payment_status == 'paid' and order.status == 'pending':
                order.status = 'confirmed'
                order.confirmed_at = timezone.now()

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    previous_status='pending',
                    new_status='confirmed',
                    changed_by=request.user,
                    reason='Payment confirmed'
                )

            order.save()

            return Response({
                'success': True,
                'message': 'Payment status updated successfully',
                'order': OrderSerializer(order).data
            })

        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


class OrderStatusHistoryView(generics.ListAPIView):
    """
    Get order status history.
    """
    serializer_class = OrderStatusHistorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        order_number = self.kwargs['order_number']

        # Check permissions
        if self.request.user.user_type == 'STAFF':
            order = get_object_or_404(Order, order_number=order_number)
        elif self.request.user.user_type == 'PROVIDER':
            order = get_object_or_404(Order, order_number=order_number, assigned_provider=self.request.user)
        else:  # customer
            order = get_object_or_404(Order, order_number=order_number, customer=self.request.user)

        return order.status_history.all()


class OrderRescheduleListView(generics.ListAPIView):
    """
    List reschedule requests for an order.
    """
    serializer_class = OrderRescheduleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        order_number = self.kwargs['order_number']

        # Check permissions
        if self.request.user.user_type == 'staff':
            order = get_object_or_404(Order, order_number=order_number)
        else:  # customer
            order = get_object_or_404(Order, order_number=order_number, customer=self.request.user)

        return order.reschedules.all()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def order_dashboard(request):
    """
    Get order dashboard statistics.
    """
    user = request.user

    if user.user_type == 'STAFF':
        # Admin dashboard
        total_orders = Order.objects.count()
        pending_orders = Order.objects.filter(status='pending').count()
        confirmed_orders = Order.objects.filter(status='confirmed').count()
        in_progress_orders = Order.objects.filter(status='in_progress').count()
        completed_orders = Order.objects.filter(status='completed').count()
        cancelled_orders = Order.objects.filter(status='cancelled').count()

        return Response({
            'total_orders': total_orders,
            'pending_orders': pending_orders,
            'confirmed_orders': confirmed_orders,
            'in_progress_orders': in_progress_orders,
            'completed_orders': completed_orders,
            'cancelled_orders': cancelled_orders,
            'recent_orders': OrderListSerializer(
                Order.objects.all()[:10], many=True
            ).data
        })

    elif user.user_type == 'PROVIDER':
        # Provider dashboard
        assigned_orders = Order.objects.filter(assigned_provider=user)
        pending_orders = assigned_orders.filter(status='assigned').count()
        in_progress_orders = assigned_orders.filter(status='in_progress').count()
        completed_orders = assigned_orders.filter(status='completed').count()

        return Response({
            'assigned_orders': assigned_orders.count(),
            'pending_orders': pending_orders,
            'in_progress_orders': in_progress_orders,
            'completed_orders': completed_orders,
            'recent_orders': ProviderOrderSerializer(
                assigned_orders[:10], many=True
            ).data
        })

    else:  # customer
        # Customer dashboard
        user_orders = Order.objects.filter(customer=user)
        pending_orders = user_orders.filter(status__in=['pending', 'confirmed']).count()
        in_progress_orders = user_orders.filter(status__in=['assigned', 'in_progress']).count()
        completed_orders = user_orders.filter(status='completed').count()

        return Response({
            'total_orders': user_orders.count(),
            'pending_orders': pending_orders,
            'in_progress_orders': in_progress_orders,
            'completed_orders': completed_orders,
            'recent_orders': CustomerOrderSerializer(
                user_orders[:5], many=True
            ).data
        })


# Admin AJAX endpoints for enhanced order creation
@staff_member_required
def get_services_for_admin(request):
    """
    AJAX endpoint to get services for admin order creation
    """
    try:
        catalogue_db = connections['catalogue_db']
        with catalogue_db.cursor() as cursor:
            cursor.execute("""
                SELECT s.id, s.title, s.base_price, s.discount_price,
                       s.time_to_complete, c.name as category_name
                FROM catalogue_service s
                JOIN catalogue_category c ON s.category_id = c.id
                WHERE s.is_active = true
                ORDER BY c.name, s.title
            """)
            services = cursor.fetchall()

            service_data = {}
            for service in services:
                service_id, title, base_price, discount_price, time_to_complete, category_name = service
                service_data[str(service_id)] = {
                    'id': service_id,
                    'title': title,
                    'base_price': str(base_price),
                    'discount_price': str(discount_price) if discount_price else None,
                    'time_to_complete': str(time_to_complete) if time_to_complete else None,
                    'category_name': category_name
                }

            return JsonResponse(service_data)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@staff_member_required
def calculate_tax_for_admin(request):
    """
    AJAX endpoint to calculate tax for admin order creation
    """
    if request.method == 'POST':
        try:
            amount = Decimal(request.POST.get('amount', '0'))

            # Use taxation service to calculate tax
            from taxation.services import TaxCalculationService
            tax_calculation = TaxCalculationService.calculate_total_tax_and_charges(amount)

            return JsonResponse({
                'success': True,
                'total_tax': str(tax_calculation['total_gst']),
                'cgst': str(tax_calculation.get('cgst', '0.00')),
                'sgst': str(tax_calculation.get('sgst', '0.00')),
                'igst': str(tax_calculation.get('igst', '0.00')),
                'service_charge': str(tax_calculation.get('service_charge', '0.00')),
                'grand_total': str(tax_calculation['grand_total'])
            })
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)

    return JsonResponse({'success': False, 'error': 'Invalid request method'}, status=405)


@staff_member_required
def apply_coupon_for_admin(request):
    """
    AJAX endpoint to apply coupon for admin order creation
    """
    if request.method == 'POST':
        try:
            coupon_code = request.POST.get('coupon_code', '').upper()
            amount = Decimal(request.POST.get('amount', '0'))

            from coupons.models import Coupon
            coupon = Coupon.objects.get(code=coupon_code, is_active=True)

            if coupon.is_valid():
                discount_amount = coupon.calculate_discount(amount)
                return JsonResponse({
                    'success': True,
                    'discount_amount': str(discount_amount),
                    'message': f'Coupon {coupon_code} applied successfully'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'Coupon is not valid or has expired'
                })

        except Coupon.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Invalid coupon code'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': str(e)
            })

    return JsonResponse({'success': False, 'message': 'Invalid request method'}, status=405)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def accept_order(request, order_number):
    """
    Accept a pending order (Staff only).
    """
    try:
        order = Order.objects.get(order_number=order_number)

        if order.status != 'pending':
            return Response({
                'success': False,
                'message': 'Order can only be accepted from pending status'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = AcceptOrderSerializer(data=request.data)
        if serializer.is_valid():
            notes = serializer.validated_data.get('notes', '')

            # Update order
            order.status = 'accepted'
            order.accepted_at = timezone.now()
            if notes:
                order.admin_notes = notes
            order.save()

            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status='pending',
                new_status='accepted',
                changed_by=request.user,
                reason='Order accepted by staff'
            )

            return Response({
                'success': True,
                'message': 'Order accepted successfully',
                'order': OrderSerializer(order).data
            })

        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_order(request, order_number):
    """
    Complete an order (Staff or Provider).
    """
    try:
        order = Order.objects.get(order_number=order_number)

        # Check permissions
        if not (request.user.user_type == 'staff' or
                (request.user.user_type == 'provider' and order.assigned_provider == request.user)):
            return Response({
                'success': False,
                'message': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        if order.status != 'vendor_assigned':
            return Response({
                'success': False,
                'message': 'Order can only be completed from vendor assigned status'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = CompleteOrderSerializer(data=request.data)
        if serializer.is_valid():
            payment_received = serializer.validated_data.get('payment_received', False)
            notes = serializer.validated_data.get('notes', '')

            # Update order status based on payment
            if payment_received or order.payment_method == 'razorpay':
                order.status = 'completed_payment_received'
                order.payment_status = 'paid_online'
            else:
                order.status = 'completed_awaiting_payment'
                order.payment_status = 'cod'

            order.completed_at = timezone.now()
            if notes:
                order.admin_notes = notes
            order.save()

            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status='vendor_assigned',
                new_status=order.status,
                changed_by=request.user,
                reason='Order completed'
            )

            return Response({
                'success': True,
                'message': 'Order completed successfully',
                'order': OrderSerializer(order).data
            })

        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def hold_order(request, order_number):
    """
    Put an order on hold (Staff only).
    """
    try:
        order = Order.objects.get(order_number=order_number)

        if not order.can_put_on_hold():
            return Response({
                'success': False,
                'message': 'Order cannot be put on hold in current status'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = HoldOrderSerializer(data=request.data)
        if serializer.is_valid():
            reason = serializer.validated_data['reason']
            description = serializer.validated_data.get('description', '')
            expected_resolution_date = serializer.validated_data.get('expected_resolution_date')

            # Update order
            previous_status = order.status
            order.status = 'on_hold'
            order.hold_reason = description
            order.held_at = timezone.now()
            order.held_by = request.user
            order.save()

            # Create hold record
            OrderHold.objects.create(
                order=order,
                reason=reason,
                description=description,
                held_by=request.user,
                expected_resolution_date=expected_resolution_date
            )

            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status=previous_status,
                new_status='on_hold',
                changed_by=request.user,
                reason=f'Order put on hold: {reason}'
            )

            return Response({
                'success': True,
                'message': 'Order put on hold successfully',
                'order': OrderSerializer(order).data
            })

        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_incomplete(request, order_number):
    """
    Mark an order as incomplete with pending works (Staff or Provider).
    """
    try:
        order = Order.objects.get(order_number=order_number)

        # Check permissions
        if not (request.user.user_type == 'staff' or
                (request.user.user_type == 'provider' and order.assigned_provider == request.user)):
            return Response({
                'success': False,
                'message': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        if not order.can_mark_incomplete():
            return Response({
                'success': False,
                'message': 'Order cannot be marked incomplete in current status'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = MarkIncompleteSerializer(data=request.data)
        if serializer.is_valid():
            reason = serializer.validated_data['reason']
            pending_work_description = serializer.validated_data['pending_work_description']
            estimated_completion_time = serializer.validated_data.get('estimated_completion_time', '')
            additional_cost = serializer.validated_data.get('additional_cost', 0)
            expected_completion_date = serializer.validated_data.get('expected_completion_date')

            # Update order
            previous_status = order.status
            order.status = 'incomplete_pending_works'
            order.incomplete_work_details = pending_work_description
            order.marked_incomplete_at = timezone.now()
            order.marked_incomplete_by = request.user
            order.save()

            # Create incomplete work record
            OrderIncompleteWork.objects.create(
                order=order,
                reason=reason,
                pending_work_description=pending_work_description,
                estimated_completion_time=estimated_completion_time,
                additional_cost=additional_cost,
                marked_by=request.user,
                expected_completion_date=expected_completion_date
            )

            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status=previous_status,
                new_status='incomplete_pending_works',
                changed_by=request.user,
                reason=f'Order marked incomplete: {reason}'
            )

            return Response({
                'success': True,
                'message': 'Order marked as incomplete successfully',
                'order': OrderSerializer(order).data
            })

        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)
