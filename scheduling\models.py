from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from datetime import datetime, time, timedelta
import uuid


class SlotConfiguration(models.Model):
    """
    Global configuration for slot management system.
    """
    INTERVAL_CHOICES = [
        (15, '15 minutes'),
        (30, '30 minutes'),
        (60, '1 hour'),
        (90, '1.5 hours'),
        (120, '2 hours'),
    ]
    
    BUFFER_TIME_CHOICES = [
        (0, 'No buffer'),
        (15, '15 minutes'),
        (30, '30 minutes'),
        (45, '45 minutes'),
        (60, '1 hour'),
    ]
    
    name = models.CharField(max_length=100, unique=True, help_text="Configuration name")
    slot_interval_minutes = models.IntegerField(
        choices=INTERVAL_CHOICES,
        default=60,
        help_text="Duration of each time slot in minutes"
    )
    buffer_time_minutes = models.IntegerField(
        choices=BUFFER_TIME_CHOICES,
        default=15,
        help_text="Buffer time at the beginning of each shift"
    )
    advance_booking_days = models.IntegerField(
        default=30,
        validators=[MinValueValidator(1), MaxValueValidator(365)],
        help_text="How many days in advance customers can book"
    )
    same_day_booking_cutoff_hours = models.IntegerField(
        default=2,
        validators=[MinValueValidator(0), MaxValueValidator(24)],
        help_text="Hours before which same-day booking is not allowed"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Slot Configuration"
        verbose_name_plural = "Slot Configurations"
        ordering = ['-is_active', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.slot_interval_minutes}min slots)"


class WorkingShift(models.Model):
    """
    Define working hours for different days of the week.
    """
    WEEKDAY_CHOICES = [
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    ]
    
    configuration = models.ForeignKey(
        SlotConfiguration,
        on_delete=models.CASCADE,
        related_name='working_shifts'
    )
    weekday = models.IntegerField(choices=WEEKDAY_CHOICES)
    start_time = models.TimeField(help_text="Shift start time")
    end_time = models.TimeField(help_text="Shift end time")
    is_working_day = models.BooleanField(default=True)
    max_bookings_per_slot = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text="Maximum bookings allowed per time slot"
    )
    
    class Meta:
        unique_together = ('configuration', 'weekday')
        verbose_name = "Working Shift"
        verbose_name_plural = "Working Shifts"
        ordering = ['weekday', 'start_time']
    
    def __str__(self):
        day_name = dict(self.WEEKDAY_CHOICES)[self.weekday]
        if self.is_working_day:
            return f"{day_name}: {self.start_time} - {self.end_time}"
        else:
            return f"{day_name}: Closed"


class HolidaySchedule(models.Model):
    """
    Manage holidays and non-working days.
    """
    HOLIDAY_TYPE_CHOICES = [
        ('national', 'National Holiday'),
        ('regional', 'Regional Holiday'),
        ('company', 'Company Holiday'),
        ('maintenance', 'Maintenance Day'),
        ('custom', 'Custom Closure'),
    ]
    
    name = models.CharField(max_length=200, help_text="Holiday/closure name")
    date = models.DateField(help_text="Holiday date")
    holiday_type = models.CharField(max_length=20, choices=HOLIDAY_TYPE_CHOICES, default='custom')
    is_recurring = models.BooleanField(default=False, help_text="Repeats every year")
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('date', 'name')
        verbose_name = "Holiday Schedule"
        verbose_name_plural = "Holiday Schedules"
        ordering = ['date']
    
    def __str__(self):
        return f"{self.name} - {self.date}"


class TimeSlot(models.Model):
    """
    Individual time slots with availability status.
    """
    STATUS_CHOICES = [
        ('available', 'Available'),
        ('booked', 'Booked'),
        ('blocked', 'Blocked'),
        ('maintenance', 'Maintenance'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    configuration = models.ForeignKey(
        SlotConfiguration,
        on_delete=models.CASCADE,
        related_name='time_slots'
    )
    date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='available')
    current_bookings = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    max_bookings = models.IntegerField(default=1, validators=[MinValueValidator(1)])
    
    # Booking references
    booked_orders = models.JSONField(default=list, blank=True, help_text="List of order IDs")
    
    # Administrative fields
    blocked_reason = models.CharField(max_length=200, blank=True, null=True)
    blocked_by = models.CharField(max_length=100, blank=True, null=True)  # Staff username
    notes = models.TextField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('configuration', 'date', 'start_time')
        verbose_name = "Time Slot"
        verbose_name_plural = "Time Slots"
        ordering = ['date', 'start_time']
        indexes = [
            models.Index(fields=['date', 'status']),
            models.Index(fields=['configuration', 'date']),
        ]
    
    def __str__(self):
        return f"{self.date} {self.start_time}-{self.end_time} ({self.status})"
    
    @property
    def is_available(self):
        """Check if slot is available for booking."""
        return (
            self.status == 'available' and 
            self.current_bookings < self.max_bookings and
            self.date >= timezone.now().date()
        )
    
    @property
    def remaining_capacity(self):
        """Get remaining booking capacity."""
        return max(0, self.max_bookings - self.current_bookings)
    
    def can_book(self, quantity=1):
        """Check if specified quantity can be booked."""
        return self.is_available and self.remaining_capacity >= quantity


class SlotBlockage(models.Model):
    """
    Block specific slots or time ranges for maintenance, holidays, etc.
    """
    BLOCKAGE_TYPE_CHOICES = [
        ('single_slot', 'Single Time Slot'),
        ('time_range', 'Time Range'),
        ('full_day', 'Full Day'),
        ('recurring', 'Recurring Pattern'),
    ]
    
    RECURRENCE_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('custom', 'Custom Pattern'),
    ]
    
    name = models.CharField(max_length=200, help_text="Blockage description")
    blockage_type = models.CharField(max_length=20, choices=BLOCKAGE_TYPE_CHOICES)
    
    # Date and time fields
    start_date = models.DateField()
    end_date = models.DateField(blank=True, null=True)
    start_time = models.TimeField(blank=True, null=True)
    end_time = models.TimeField(blank=True, null=True)
    
    # Recurrence settings
    is_recurring = models.BooleanField(default=False)
    recurrence_pattern = models.CharField(
        max_length=20, 
        choices=RECURRENCE_CHOICES, 
        blank=True, 
        null=True
    )
    recurrence_end_date = models.DateField(blank=True, null=True)
    
    # Administrative fields
    reason = models.TextField(help_text="Reason for blockage")
    blocked_by = models.CharField(max_length=100, help_text="Staff username who created this blockage")
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Slot Blockage"
        verbose_name_plural = "Slot Blockages"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.start_date})"


class SlotBooking(models.Model):
    """
    Track slot bookings and their status.
    """
    BOOKING_STATUS_CHOICES = [
        ('pending', 'Pending Confirmation'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
        ('no_show', 'No Show'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    time_slot = models.ForeignKey(
        TimeSlot,
        on_delete=models.CASCADE,
        related_name='bookings'
    )
    
    # Order reference (cross-database)
    order_id = models.CharField(max_length=100, help_text="Order ID from orders database")
    customer_mobile = models.CharField(max_length=15, help_text="Customer mobile number")
    customer_name = models.CharField(max_length=100, help_text="Customer name")
    
    # Booking details
    booking_status = models.CharField(max_length=20, choices=BOOKING_STATUS_CHOICES, default='pending')
    quantity = models.IntegerField(default=1, validators=[MinValueValidator(1)])
    service_names = models.JSONField(default=list, help_text="List of service names")
    
    # Timestamps
    booked_at = models.DateTimeField(auto_now_add=True)
    confirmed_at = models.DateTimeField(blank=True, null=True)
    cancelled_at = models.DateTimeField(blank=True, null=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    # Notes
    customer_notes = models.TextField(blank=True, null=True)
    admin_notes = models.TextField(blank=True, null=True)
    
    class Meta:
        verbose_name = "Slot Booking"
        verbose_name_plural = "Slot Bookings"
        ordering = ['-booked_at']
        indexes = [
            models.Index(fields=['order_id']),
            models.Index(fields=['customer_mobile']),
            models.Index(fields=['booking_status']),
        ]
    
    def __str__(self):
        return f"Booking {self.order_id} - {self.time_slot}"
